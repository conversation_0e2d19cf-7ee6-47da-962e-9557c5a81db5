<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('generated_documents', function (Blueprint $table) {
            $table->id();
            $table->string('pegawai_nip', 20)->index();
            $table->string('pegawai_nama');
            $table->string('pegawai_golongan', 10)->nullable();
            $table->text('pegawai_jabatan')->nullable();
            $table->string('pegawai_unit_kerja')->nullable();

            $table->string('pejabat_nip', 20)->nullable();
            $table->string('pejabat_nama')->nullable();
            $table->string('pejabat_golongan', 10)->nullable();
            $table->text('pejabat_jabatan')->nullable();
            $table->string('pejabat_unit_kerja')->nullable();

            $table->string('template_name');
            $table->string('template_filename');
            $table->string('generated_filename');
            $table->string('file_path', 500);
            $table->integer('file_size')->unsigned();

            $table->string('generated_by', 100)->nullable(); // IP address or user identifier
            $table->timestamp('generated_at');
            $table->json('template_variables')->nullable(); // Store the variables used

            $table->timestamps();

            // Indexes for better performance
            $table->index(['pegawai_nip', 'generated_at']);
            $table->index('template_name');
            $table->index('generated_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('generated_documents');
    }
};
