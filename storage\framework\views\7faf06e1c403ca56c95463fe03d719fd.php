<?php $__env->startSection('title', 'Daftar Dokumen Pegawai'); ?>

<?php $__env->startSection('content'); ?>
<div class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
    <!-- Header Section -->
    <div class="bg-white/90 backdrop-blur-md border-b border-gray-200/50 sticky top-0 z-40 shadow-sm">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <div class="w-14 h-14 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg">
                        <svg class="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                        </svg>
                    </div>
                    <div>
                        <h1 class="text-2xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent">
                            📋 Daftar Dokumen Pegawai
                        </h1>
                        <p class="text-sm text-gray-600 mt-1 flex items-center">
                            <span class="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                            Kelola dan pantau dokumen yang telah di-generate untuk setiap pegawai
                        </p>
                    </div>
                </div>
                <div class="flex items-center space-x-3">
                    <button onclick="refreshTable()" 
                            class="inline-flex items-center px-4 py-2.5 bg-gray-100 hover:bg-gray-200 text-gray-700 text-sm font-medium rounded-xl border border-gray-200 shadow-sm hover:shadow-md transition-all duration-200">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                        </svg>
                        Refresh
                    </button>
                    <a href="<?php echo e(route('documents.index')); ?>" 
                       class="inline-flex items-center px-4 py-2.5 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white text-sm font-medium rounded-xl shadow-lg hover:shadow-xl transition-all duration-200">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        Generate Dokumen
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-6">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl flex items-center justify-center">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Total Pegawai</p>
                        <p class="text-2xl font-bold text-gray-900" id="total-pegawai">-</p>
                    </div>
                </div>
            </div>

            <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-6">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-gradient-to-r from-green-500 to-green-600 rounded-xl flex items-center justify-center">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Total Dokumen</p>
                        <p class="text-2xl font-bold text-gray-900" id="total-dokumen">-</p>
                    </div>
                </div>
            </div>

            <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-6">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-gradient-to-r from-purple-500 to-purple-600 rounded-xl flex items-center justify-center">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Rata-rata per Pegawai</p>
                        <p class="text-2xl font-bold text-gray-900" id="avg-dokumen">-</p>
                    </div>
                </div>
            </div>

            <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-6">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-gradient-to-r from-orange-500 to-orange-600 rounded-xl flex items-center justify-center">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Terakhir Update</p>
                        <p class="text-sm font-bold text-gray-900" id="last-update">-</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- DataTable Container -->
        <div class="bg-white/90 backdrop-blur-sm rounded-2xl shadow-2xl border border-white/20 overflow-hidden">
            <!-- Table Header -->
            <div class="bg-gradient-to-r from-slate-800 to-slate-900 px-6 py-4">
                <h2 class="text-lg font-semibold text-white flex items-center">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                    </svg>
                    Data Pegawai & Dokumen
                </h2>
                <p class="text-slate-300 text-sm mt-1">Daftar pegawai yang memiliki dokumen yang telah di-generate</p>
            </div>

            <!-- Table Content -->
            <div class="overflow-x-auto">
                <table id="pegawaiDocumentsTable" class="w-full">
                    <thead>
                        <tr class="bg-gradient-to-r from-slate-700 to-slate-800">
                            <th class="px-6 py-4 text-left text-sm font-semibold text-white">
                                <div class="flex items-center space-x-2">
                                    <span class="text-blue-400">🆔</span>
                                    <span>NIP</span>
                                </div>
                            </th>
                            <th class="px-6 py-4 text-left text-sm font-semibold text-white">
                                <div class="flex items-center space-x-2">
                                    <span class="text-green-400">👤</span>
                                    <span>Nama Pegawai</span>
                                </div>
                            </th>
                            <th class="px-6 py-4 text-left text-sm font-semibold text-white">
                                <div class="flex items-center space-x-2">
                                    <span class="text-purple-400">🏅</span>
                                    <span>Golongan</span>
                                </div>
                            </th>
                            <th class="px-6 py-4 text-left text-sm font-semibold text-white">
                                <div class="flex items-center space-x-2">
                                    <span class="text-orange-400">💼</span>
                                    <span>Jabatan</span>
                                </div>
                            </th>
                            <th class="px-6 py-4 text-left text-sm font-semibold text-white">
                                <div class="flex items-center space-x-2">
                                    <span class="text-indigo-400">🏢</span>
                                    <span>Unit Kerja</span>
                                </div>
                            </th>
                            <th class="px-6 py-4 text-center text-sm font-semibold text-white">
                                <div class="flex items-center justify-center space-x-2">
                                    <span class="text-emerald-400">📄</span>
                                    <span>Dokumen</span>
                                </div>
                            </th>
                            <th class="px-6 py-4 text-center text-sm font-semibold text-white">
                                <div class="flex items-center justify-center space-x-2">
                                    <span class="text-yellow-400">⚡</span>
                                    <span>Aksi</span>
                                </div>
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white/50 backdrop-blur-sm">
                        <!-- Data will be loaded via DataTables -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Loading Modal -->
<div id="loadingModal" class="fixed inset-0 bg-black/50 backdrop-blur-sm hidden items-center justify-center z-50">
    <div class="bg-white rounded-2xl p-8 max-w-sm mx-4 shadow-2xl">
        <div class="text-center">
            <div class="w-16 h-16 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
            </div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">Memuat Data...</h3>
            <p class="text-sm text-gray-600">Mohon tunggu sebentar</p>
        </div>
    </div>
</div>

<!-- Documents Modal -->
<div id="documentsModal" class="fixed inset-0 bg-black/50 backdrop-blur-sm hidden items-center justify-center z-50">
    <div class="bg-white rounded-2xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden shadow-2xl">
        <!-- Modal Header -->
        <div class="px-6 py-4 bg-gradient-to-r from-slate-800 to-slate-900 text-white">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-lg font-semibold flex items-center">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        📄 Dokumen Pegawai
                    </h3>
                    <p class="text-slate-300 text-sm" id="modalPegawaiInfo"></p>
                </div>
                <button onclick="closeDocumentsModal()" class="text-white/80 hover:text-white transition-colors">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
        </div>
        
        <!-- Modal Body -->
        <div class="p-6 max-h-[70vh] overflow-y-auto bg-gradient-to-br from-gray-50 to-blue-50">
            <div id="documentsContent" class="space-y-4">
                <!-- Documents will be loaded here -->
            </div>
        </div>
        
        <!-- Modal Footer -->
        <div class="px-6 py-4 bg-gray-50 border-t border-gray-200">
            <div class="flex justify-end">
                <button onclick="closeDocumentsModal()" 
                        class="px-4 py-2 bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-700 hover:to-gray-800 text-white text-sm font-medium rounded-xl transition-all duration-200">
                    Tutup
                </button>
            </div>
        </div>
    </div>
</div>
<?php $__env->startPush('scripts'); ?>
<script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script>
$(document).ready(function() {
    let table;

    // Show loading
    function showLoading() {
        $('#loadingModal').removeClass('hidden').addClass('flex');
    }

    // Hide loading
    function hideLoading() {
        $('#loadingModal').addClass('hidden').removeClass('flex');
    }

    // Update stats
    function updateStats(data) {
        if (data && data.data) {
            const totalPegawai = data.recordsTotal || 0;
            let totalDokumen = 0;

            data.data.forEach(function(row) {
                totalDokumen += row.document_count || 0;
            });

            const avgDokumen = totalPegawai > 0 ? (totalDokumen / totalPegawai).toFixed(1) : 0;

            // Animate counters
            animateCounter('#total-pegawai', totalPegawai);
            animateCounter('#total-dokumen', totalDokumen);
            $('#avg-dokumen').text(avgDokumen);
            $('#last-update').text(new Date().toLocaleString('id-ID'));
        }
    }

    // Animate counter
    function animateCounter(selector, target) {
        const element = $(selector);
        const current = parseInt(element.text()) || 0;
        const increment = Math.ceil((target - current) / 20);

        if (current < target) {
            element.text(current + increment);
            setTimeout(() => animateCounter(selector, target), 50);
        } else {
            element.text(target);
        }
    }

    // Initialize DataTable
    table = $('#pegawaiDocumentsTable').DataTable({
        processing: true,
        serverSide: true,
        responsive: true,
        language: {
            processing: '<div class="flex items-center justify-center p-4"><div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mr-3"></div><span class="text-gray-600">Memuat data...</span></div>',
            search: "🔍 Cari:",
            lengthMenu: "Tampilkan _MENU_ data per halaman",
            info: "Menampilkan _START_ sampai _END_ dari _TOTAL_ data",
            infoEmpty: "Tidak ada data yang tersedia",
            infoFiltered: "(difilter dari _MAX_ total data)",
            paginate: {
                first: "⏮️ Pertama",
                last: "Terakhir ⏭️",
                next: "Selanjutnya ▶️",
                previous: "◀️ Sebelumnya"
            },
            emptyTable: "Tidak ada data pegawai dengan dokumen"
        },
        ajax: {
            url: '<?php echo e(route("documents.pegawai-documents")); ?>',
            type: 'GET',
            beforeSend: function() {
                showLoading();
            },
            complete: function(response) {
                hideLoading();
                updateStats(response.responseJSON);
            },
            error: function(xhr, error, thrown) {
                hideLoading();
                console.error('DataTables error:', error, thrown);

                Swal.fire({
                    icon: 'error',
                    title: '❌ Error',
                    text: 'Gagal memuat data pegawai. Silakan coba lagi.',
                    confirmButtonColor: '#EF4444',
                    customClass: {
                        popup: 'rounded-2xl'
                    }
                });
            }
        },
        columns: [
            {
                data: 'nip',
                name: 'nip',
                className: 'px-6 py-4 text-sm font-mono text-slate-800 bg-white/70',
                render: function(data, type, row) {
                    return '<span class="px-2 py-1 bg-blue-100 text-blue-800 rounded-lg text-xs font-medium">' + data + '</span>';
                }
            },
            {
                data: 'nama',
                name: 'nama',
                className: 'px-6 py-4 text-sm font-semibold text-slate-800 bg-white/70',
                render: function(data, type, row) {
                    return '<div class="flex items-center">' +
                           '<div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full flex items-center justify-center mr-3">' +
                           '<span class="text-white font-semibold text-xs">' + data.charAt(0).toUpperCase() + '</span>' +
                           '</div>' +
                           '<span class="text-slate-800 font-medium">' + data + '</span>' +
                           '</div>';
                }
            },
            {
                data: 'golongan',
                name: 'golongan',
                className: 'px-6 py-4 text-sm text-center bg-white/70',
                render: function(data, type, row) {
                    return '<span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold bg-purple-100 text-purple-800">' + data + '</span>';
                }
            },
            {
                data: 'jabatan',
                name: 'jabatan',
                className: 'px-6 py-4 text-sm text-slate-700 bg-white/70',
                render: function(data, type, row) {
                    const truncated = data && data.length > 40 ? data.substring(0, 40) + '...' : data || '-';
                    return '<span class="text-slate-700" title="' + (data || '') + '">' + truncated + '</span>';
                }
            },
            {
                data: 'unit_kerja',
                name: 'unit_kerja',
                className: 'px-6 py-4 text-sm text-slate-700 bg-white/70',
                render: function(data, type, row) {
                    const truncated = data && data.length > 30 ? data.substring(0, 30) + '...' : data || '-';
                    return '<span class="text-slate-700" title="' + (data || '') + '">' + truncated + '</span>';
                }
            },
            {
                data: 'document_count',
                name: 'document_count',
                className: 'px-6 py-4 text-sm text-center bg-white/70',
                render: function(data, type, row) {
                    const badgeColor = data > 3 ? 'bg-emerald-100 text-emerald-800' :
                                     data > 1 ? 'bg-blue-100 text-blue-800' :
                                     'bg-gray-100 text-gray-800';
                    return '<span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold ' + badgeColor + '">' +
                           '<span class="mr-1">📄</span>' + data + '</span>';
                }
            },
            {
                data: null,
                name: 'actions',
                orderable: false,
                searchable: false,
                className: 'px-6 py-4 text-sm text-center bg-white/70',
                render: function(data, type, row) {
                    if (row.document_count > 0) {
                        let actions = '<div class="flex items-center justify-center space-x-2">';

                        // View Documents Button
                        actions += '<button type="button" onclick="showDocuments(\'' + row.nip + '\', \'' + row.nama.replace(/'/g, "\\'") + '\')" ';
                        actions += 'class="inline-flex items-center px-3 py-1.5 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white text-xs font-medium rounded-lg transition-all duration-200 shadow-sm hover:shadow-md transform hover:scale-105">';
                        actions += '<svg class="w-3 h-3 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
                        actions += '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>';
                        actions += '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>';
                        actions += '</svg>👁️ Lihat</button>';

                        // Quick Download (latest document)
                        if (row.documents && row.documents[0]) {
                            const downloadUrl = '<?php echo e(route("documents.download-document", ":filename")); ?>'.replace(':filename', row.documents[0].filename);
                            actions += '<a href="' + downloadUrl + '" ';
                            actions += 'class="inline-flex items-center px-3 py-1.5 bg-gradient-to-r from-emerald-600 to-green-600 hover:from-emerald-700 hover:to-green-700 text-white text-xs font-medium rounded-lg transition-all duration-200 shadow-sm hover:shadow-md transform hover:scale-105">';
                            actions += '<svg class="w-3 h-3 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
                            actions += '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>';
                            actions += '</svg>📥 Download</a>';
                        }

                        actions += '</div>';
                        return actions;
                    }
                    return '<div class="text-center"><span class="inline-flex items-center px-2 py-1 bg-gray-100 text-gray-500 text-xs rounded-full">📭 Belum ada dokumen</span></div>';
                }
            }
        ],
        pageLength: 25,
        lengthMenu: [[10, 25, 50, 100], [10, 25, 50, 100]],
        dom: '<"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6"<"flex items-center space-x-4"l><"flex-1"f>>rtip',
        drawCallback: function() {
            // Style the search input
            $('.dataTables_filter input').addClass('px-4 py-2 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200');
            $('.dataTables_length select').addClass('px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500');

            // Style pagination
            $('.dataTables_paginate .paginate_button').addClass('px-3 py-2 mx-1 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors duration-200');
            $('.dataTables_paginate .paginate_button.current').addClass('bg-blue-600 text-white border-blue-600 hover:bg-blue-700');
        }
    });

    // Show documents modal
    window.showDocuments = function(nip, nama) {
        $('#modalPegawaiInfo').text(nama + ' (' + nip + ')');
        $('#documentsContent').html('<div class="text-center py-8"><div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div><p class="mt-2 text-gray-600">Memuat dokumen...</p></div>');
        $('#documentsModal').removeClass('hidden').addClass('flex');

        // Find documents for this pegawai from table data
        const tableData = table.rows().data();
        let pegawaiData = null;

        for (let i = 0; i < tableData.length; i++) {
            if (tableData[i].nip === nip) {
                pegawaiData = tableData[i];
                break;
            }
        }

        if (pegawaiData && pegawaiData.documents) {
            let html = '';
            pegawaiData.documents.forEach(function(doc, index) {
                const downloadUrl = '<?php echo e(route("documents.download-document", ":filename")); ?>'.replace(':filename', doc.filename);
                const fileExists = doc.file_exists ? 'text-green-600' : 'text-red-600';
                const statusIcon = doc.file_exists ? '✅' : '❌';
                const statusText = doc.file_exists ? 'File tersedia' : 'File tidak ditemukan';

                html += `
                    <div class="bg-white/80 backdrop-blur-sm rounded-xl p-4 hover:bg-white/90 transition-all duration-200 border border-white/30 shadow-sm">
                        <div class="flex items-center justify-between">
                            <div class="flex-1">
                                <div class="flex items-center space-x-2 mb-2">
                                    <span class="text-lg">📄</span>
                                    <h4 class="font-medium text-gray-900">${doc.template_name}</h4>
                                    <span class="${fileExists} text-xs font-medium">${statusIcon} ${statusText}</span>
                                </div>
                                <p class="text-sm text-gray-600 mb-1">
                                    <span class="font-mono bg-gray-100 px-2 py-1 rounded text-xs">${doc.filename}</span>
                                </p>
                                <div class="flex items-center space-x-4 text-xs text-gray-500">
                                    <span class="flex items-center"><svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path></svg>${doc.created_at}</span>
                                    <span class="flex items-center"><svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path></svg>${formatFileSize(doc.size)}</span>
                                </div>
                            </div>
                            <div class="flex items-center space-x-2 ml-4">
                                ${doc.file_exists ?
                                    `<a href="${downloadUrl}"
                                       class="inline-flex items-center px-3 py-2 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white text-sm font-medium rounded-lg transition-all duration-200 shadow-sm hover:shadow-md">
                                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                        </svg>
                                        Download
                                    </a>` :
                                    `<span class="inline-flex items-center px-3 py-2 bg-gray-300 text-gray-500 text-sm font-medium rounded-lg cursor-not-allowed">
                                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728"></path>
                                        </svg>
                                        Tidak Tersedia
                                    </span>`
                                }
                            </div>
                        </div>
                    </div>
                `;
            });

            if (html === '') {
                html = '<div class="text-center py-8 text-gray-500">Belum ada dokumen yang di-generate</div>';
            }

            $('#documentsContent').html(html);
        } else {
            $('#documentsContent').html('<div class="text-center py-8 text-gray-500">Belum ada dokumen yang di-generate</div>');
        }
    };

    // Close documents modal
    window.closeDocumentsModal = function() {
        $('#documentsModal').addClass('hidden').removeClass('flex');
    };

    // Format file size
    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // Refresh table function
    window.refreshTable = function() {
        table.ajax.reload(null, false);
    };

    // Close modal when clicking outside
    $(document).on('click', function(e) {
        if ($(e.target).is('#documentsModal')) {
            closeDocumentsModal();
        }
    });
});

// Global refresh function
function refreshTable() {
    if (typeof table !== 'undefined') {
        table.ajax.reload(null, false);
    }
}
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Documents\code\mutasites\resources\views/documents/list.blade.php ENDPATH**/ ?>