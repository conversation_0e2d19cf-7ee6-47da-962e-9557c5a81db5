<?php $__env->startSection('title', 'Daftar Dokumen Pegawai'); ?>

<?php $__env->startSection('content'); ?>
<div class="container mx-auto px-4 py-8">
    <div class="max-w-7xl mx-auto">
        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-2">Daftar Dokumen Pegawai</h1>
            <p class="text-gray-600">Lihat dan kelola dokumen yang telah di-generate untuk setiap pegawai</p>
            
            <!-- Navigation -->
            <div class="mt-4">
                <a href="<?php echo e(route('documents.index')); ?>" 
                   class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                    </svg>
                    Generate Dokumen Baru
                </a>
            </div>
        </div>

        <!-- DataTable -->
        <div class="bg-white rounded-lg shadow-lg overflow-hidden">
            <div class="p-6">
                <table id="pegawaiDocumentsTable" class="w-full table-auto">
                    <thead>
                        <tr class="bg-gray-50">
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                NIP
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Nama Pegawai
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Golongan
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Jabatan
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Unit Kerja
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Jumlah Dokumen
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Aksi
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <!-- Data will be loaded via DataTables -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Loading Modal -->
<div id="loadingModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden items-center justify-center z-50">
    <div class="bg-white rounded-lg p-6 max-w-sm mx-auto">
        <div class="flex items-center">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mr-3"></div>
            <span class="text-gray-700">Memuat data...</span>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<!-- DataTables CSS -->
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.13.6/css/dataTables.tailwindcss.min.css">
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/responsive/2.5.0/css/responsive.dataTables.min.css">
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<!-- DataTables JS -->
<script type="text/javascript" src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
<script type="text/javascript" src="https://cdn.datatables.net/1.13.6/js/dataTables.tailwindcss.min.js"></script>
<script type="text/javascript" src="https://cdn.datatables.net/responsive/2.5.0/js/dataTables.responsive.min.js"></script>

<script>
$(document).ready(function() {
    // Show loading modal
    function showLoading() {
        $('#loadingModal').removeClass('hidden').addClass('flex');
    }
    
    // Hide loading modal
    function hideLoading() {
        $('#loadingModal').addClass('hidden').removeClass('flex');
    }

    // Initialize DataTable
    const table = $('#pegawaiDocumentsTable').DataTable({
        processing: true,
        serverSide: true,
        responsive: true,
        ajax: {
            url: '<?php echo e(route("documents.pegawai-documents")); ?>',
            type: 'GET',
            beforeSend: function() {
                showLoading();
            },
            complete: function() {
                hideLoading();
            },
            error: function(xhr, error, thrown) {
                hideLoading();
                console.error('DataTables error:', error, thrown);
                
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: 'Gagal memuat data pegawai. Silakan coba lagi.',
                    confirmButtonColor: '#3B82F6'
                });
            }
        },
        columns: [
            { 
                data: 'nip',
                name: 'nip',
                className: 'font-mono text-sm'
            },
            { 
                data: 'nama',
                name: 'nama',
                className: 'font-medium'
            },
            { 
                data: 'golongan',
                name: 'golongan',
                className: 'text-center'
            },
            { 
                data: 'jabatan',
                name: 'jabatan',
                className: 'text-sm',
                render: function(data, type, row) {
                    if (data && data.length > 50) {
                        return '<span title="' + data + '">' + data.substring(0, 50) + '...</span>';
                    }
                    return data || '-';
                }
            },
            { 
                data: 'unit_kerja',
                name: 'unit_kerja',
                className: 'text-sm',
                render: function(data, type, row) {
                    if (data && data.length > 30) {
                        return '<span title="' + data + '">' + data.substring(0, 30) + '...</span>';
                    }
                    return data || '-';
                }
            },
            { 
                data: 'document_count',
                name: 'document_count',
                className: 'text-center',
                render: function(data, type, row) {
                    if (data > 0) {
                        return '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">' + data + ' dokumen</span>';
                    }
                    return '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">0 dokumen</span>';
                }
            },
            { 
                data: null,
                name: 'actions',
                orderable: false,
                searchable: false,
                className: 'text-center',
                render: function(data, type, row) {
                    if (row.document_count > 0) {
                        let dropdown = '<div class="relative inline-block text-left">';
                        dropdown += '<button type="button" class="inline-flex justify-center w-full rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" onclick="toggleDropdown(this)">';
                        dropdown += 'Lihat Dokumen <svg class="-mr-1 ml-2 h-5 w-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg>';
                        dropdown += '</button>';
                        dropdown += '<div class="dropdown-menu origin-top-right absolute right-0 mt-2 w-56 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 hidden z-10">';
                        dropdown += '<div class="py-1">';
                        
                        row.documents.forEach(function(doc) {
                            const downloadUrl = '<?php echo e(route("documents.download-document", ":filename")); ?>'.replace(':filename', doc.filename);
                            dropdown += '<a href="' + downloadUrl + '" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">';
                            dropdown += '<div class="flex items-center">';
                            dropdown += '<svg class="w-4 h-4 mr-2 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path></svg>';
                            dropdown += '<div>';
                            dropdown += '<div class="font-medium">' + doc.template_name + '</div>';
                            dropdown += '<div class="text-xs text-gray-500">' + doc.created_at + '</div>';
                            dropdown += '</div>';
                            dropdown += '</div>';
                            dropdown += '</a>';
                        });
                        
                        dropdown += '</div></div></div>';
                        return dropdown;
                    }
                    return '<span class="text-gray-400 text-sm">Tidak ada dokumen</span>';
                }
            }
        ],
        pageLength: 25,
        lengthMenu: [[10, 25, 50, 100], [10, 25, 50, 100]],
        language: {
            processing: "Memproses...",
            search: "Cari:",
            lengthMenu: "Tampilkan _MENU_ data per halaman",
            info: "Menampilkan _START_ sampai _END_ dari _TOTAL_ data",
            infoEmpty: "Menampilkan 0 sampai 0 dari 0 data",
            infoFiltered: "(disaring dari _MAX_ total data)",
            loadingRecords: "Memuat...",
            zeroRecords: "Tidak ada data yang ditemukan",
            emptyTable: "Tidak ada data tersedia",
            paginate: {
                first: "Pertama",
                previous: "Sebelumnya",
                next: "Selanjutnya",
                last: "Terakhir"
            }
        },
        order: [[1, 'asc']] // Sort by nama
    });

    // Handle dropdown toggle
    window.toggleDropdown = function(button) {
        const dropdown = button.nextElementSibling;
        const isHidden = dropdown.classList.contains('hidden');
        
        // Close all other dropdowns
        document.querySelectorAll('.dropdown-menu').forEach(menu => {
            menu.classList.add('hidden');
        });
        
        // Toggle current dropdown
        if (isHidden) {
            dropdown.classList.remove('hidden');
        }
    };

    // Close dropdown when clicking outside
    document.addEventListener('click', function(event) {
        if (!event.target.closest('.relative')) {
            document.querySelectorAll('.dropdown-menu').forEach(menu => {
                menu.classList.add('hidden');
            });
        }
    });
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Documents\code\mutasites\resources\views/documents/list.blade.php ENDPATH**/ ?>