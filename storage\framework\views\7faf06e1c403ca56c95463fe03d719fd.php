<?php $__env->startSection('title', 'Daftar Dokumen Pegawai'); ?>

<?php $__env->startSection('content'); ?>
<div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <div class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">📋 Daftar Dokumen Pegawai</h1>
                    <p class="text-gray-600 mt-1">Kelola dan pantau dokumen yang telah di-generate</p>
                </div>
                <div class="flex space-x-3">
                    <button onclick="refreshTable()" 
                            class="px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg transition-colors">
                        🔄 Refresh
                    </button>
                    <a href="<?php echo e(route('documents.index')); ?>" 
                       class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors">
                        ➕ Generate Dokumen
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Stats -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-3 bg-blue-100 rounded-full">
                        <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm text-gray-600">Total Pegawai</p>
                        <p class="text-2xl font-bold text-gray-900" id="total-pegawai">-</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-3 bg-green-100 rounded-full">
                        <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm text-gray-600">Total Dokumen</p>
                        <p class="text-2xl font-bold text-gray-900" id="total-dokumen">-</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-3 bg-purple-100 rounded-full">
                        <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm text-gray-600">Rata-rata</p>
                        <p class="text-2xl font-bold text-gray-900" id="avg-dokumen">-</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-3 bg-orange-100 rounded-full">
                        <svg class="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm text-gray-600">Update</p>
                        <p class="text-sm font-bold text-gray-900" id="last-update">-</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- DataTable -->
        <div class="bg-white rounded-lg shadow overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-900">Data Pegawai & Dokumen</h2>
            </div>
            <div class="overflow-x-auto">
                <table id="pegawaiDocumentsTable" class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">NIP</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Nama Pegawai</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Golongan</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Jabatan</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Unit Kerja</th>
                            <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Dokumen</th>
                            <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Aksi</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <!-- Data will be loaded via DataTables -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Loading Modal -->
<div id="loadingModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
    <div class="bg-white rounded-lg p-6 max-w-sm mx-4">
        <div class="flex items-center">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mr-3"></div>
            <span class="text-gray-700">Memuat data...</span>
        </div>
    </div>
</div>

<!-- Documents Modal -->
<div id="documentsModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
    <div class="bg-white rounded-lg max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden">
        <!-- Modal Header -->
        <div class="px-6 py-4 bg-gray-50 border-b">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-lg font-semibold text-gray-900">📄 Dokumen Pegawai</h3>
                    <p class="text-gray-600 text-sm" id="modalPegawaiInfo"></p>
                </div>
                <button onclick="closeDocumentsModal()" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
        </div>
        
        <!-- Modal Body -->
        <div class="p-6 max-h-[70vh] overflow-y-auto">
            <div id="documentsContent" class="space-y-4">
                <!-- Documents will be loaded here -->
            </div>
        </div>
        
        <!-- Modal Footer -->
        <div class="px-6 py-4 bg-gray-50 border-t">
            <div class="flex justify-end">
                <button onclick="closeDocumentsModal()" 
                        class="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors">
                    Tutup
                </button>
            </div>
        </div>
    </div>
</div>
<?php $__env->startPush('scripts'); ?>
<script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script>
$(document).ready(function() {
    let table;

    // Show loading
    function showLoading() {
        $('#loadingModal').removeClass('hidden').addClass('flex');
    }

    // Hide loading
    function hideLoading() {
        $('#loadingModal').addClass('hidden').removeClass('flex');
    }

    // Update stats
    function updateStats(data) {
        if (data && data.data) {
            const totalPegawai = data.recordsTotal || 0;
            let totalDokumen = 0;

            data.data.forEach(function(row) {
                totalDokumen += row.document_count || 0;
            });

            const avgDokumen = totalPegawai > 0 ? (totalDokumen / totalPegawai).toFixed(1) : 0;

            $('#total-pegawai').text(totalPegawai);
            $('#total-dokumen').text(totalDokumen);
            $('#avg-dokumen').text(avgDokumen);
            $('#last-update').text(new Date().toLocaleString('id-ID'));
        }
    }

    // Initialize DataTable
    table = $('#pegawaiDocumentsTable').DataTable({
        processing: true,
        serverSide: true,
        responsive: true,
        language: {
            processing: 'Memuat data...',
            search: "Cari:",
            lengthMenu: "Tampilkan _MENU_ data",
            info: "Menampilkan _START_ sampai _END_ dari _TOTAL_ data",
            infoEmpty: "Tidak ada data",
            infoFiltered: "(difilter dari _MAX_ total data)",
            paginate: {
                first: "Pertama",
                last: "Terakhir",
                next: "Selanjutnya",
                previous: "Sebelumnya"
            },
            emptyTable: "Tidak ada data pegawai dengan dokumen"
        },
        ajax: {
            url: '<?php echo e(route("documents.pegawai-documents")); ?>',
            type: 'GET',
            beforeSend: function() {
                showLoading();
            },
            complete: function(response) {
                hideLoading();
                updateStats(response.responseJSON);
            },
            error: function(xhr, error, thrown) {
                hideLoading();
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: 'Gagal memuat data pegawai. Silakan coba lagi.',
                    confirmButtonColor: '#EF4444'
                });
            }
        },
        columns: [
            {
                data: 'nip',
                name: 'nip',
                className: 'px-6 py-4 text-sm font-mono',
                render: function(data, type, row) {
                    return '<span class="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs font-medium">' + data + '</span>';
                }
            },
            {
                data: 'nama',
                name: 'nama',
                className: 'px-6 py-4 text-sm font-semibold'
            },
            {
                data: 'golongan',
                name: 'golongan',
                className: 'px-6 py-4 text-sm text-center',
                render: function(data, type, row) {
                    return '<span class="px-2 py-1 bg-purple-100 text-purple-800 rounded text-xs font-medium">' + data + '</span>';
                }
            },
            {
                data: 'jabatan',
                name: 'jabatan',
                className: 'px-6 py-4 text-sm',
                render: function(data, type, row) {
                    const truncated = data && data.length > 40 ? data.substring(0, 40) + '...' : data || '-';
                    return '<span title="' + (data || '') + '">' + truncated + '</span>';
                }
            },
            {
                data: 'unit_kerja',
                name: 'unit_kerja',
                className: 'px-6 py-4 text-sm',
                render: function(data, type, row) {
                    const truncated = data && data.length > 30 ? data.substring(0, 30) + '...' : data || '-';
                    return '<span title="' + (data || '') + '">' + truncated + '</span>';
                }
            },
            {
                data: 'document_count',
                name: 'document_count',
                className: 'px-6 py-4 text-sm text-center',
                render: function(data, type, row) {
                    const badgeColor = data > 3 ? 'bg-green-100 text-green-800' :
                                     data > 1 ? 'bg-blue-100 text-blue-800' :
                                     'bg-gray-100 text-gray-800';
                    return '<span class="px-2 py-1 rounded text-xs font-medium ' + badgeColor + '">' + data + '</span>';
                }
            },
            {
                data: null,
                name: 'actions',
                orderable: false,
                searchable: false,
                className: 'px-6 py-4 text-sm text-center',
                render: function(data, type, row) {
                    if (row.document_count > 0) {
                        let actions = '<div class="flex justify-center space-x-2">';

                        // View Documents Button
                        actions += '<button type="button" onclick="showDocuments(\'' + row.nip + '\', \'' + row.nama.replace(/'/g, "\\'") + '\')" ';
                        actions += 'class="px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white text-xs rounded transition-colors">Lihat</button>';

                        // Quick Download (latest document)
                        if (row.documents && row.documents[0]) {
                            const downloadUrl = '<?php echo e(route("documents.download-document", ":filename")); ?>'.replace(':filename', row.documents[0].filename);
                            actions += '<a href="' + downloadUrl + '" ';
                            actions += 'class="px-3 py-1 bg-green-600 hover:bg-green-700 text-white text-xs rounded transition-colors">Download</a>';
                        }

                        actions += '</div>';
                        return actions;
                    }
                    return '<span class="text-gray-500 text-xs">Belum ada dokumen</span>';
                }
            }
        ],
        pageLength: 25,
        lengthMenu: [[10, 25, 50, 100], [10, 25, 50, 100]]
    });

    // Show documents modal
    window.showDocuments = function(nip, nama) {
        $('#modalPegawaiInfo').text(nama + ' (' + nip + ')');
        $('#documentsContent').html('<div class="text-center py-8"><div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div><p class="mt-2 text-gray-600">Memuat dokumen...</p></div>');
        $('#documentsModal').removeClass('hidden').addClass('flex');

        // Find documents for this pegawai from table data
        const tableData = table.rows().data();
        let pegawaiData = null;

        for (let i = 0; i < tableData.length; i++) {
            if (tableData[i].nip === nip) {
                pegawaiData = tableData[i];
                break;
            }
        }

        if (pegawaiData && pegawaiData.documents) {
            let html = '';
            pegawaiData.documents.forEach(function(doc, index) {
                const downloadUrl = '<?php echo e(route("documents.download-document", ":filename")); ?>'.replace(':filename', doc.filename);
                const statusIcon = doc.file_exists ? '✅' : '❌';
                const statusText = doc.file_exists ? 'File tersedia' : 'File tidak ditemukan';

                html += `
                    <div class="bg-gray-50 rounded-lg p-4 border">
                        <div class="flex items-center justify-between">
                            <div class="flex-1">
                                <h4 class="font-medium text-gray-900 mb-1">${doc.template_name}</h4>
                                <p class="text-sm text-gray-600 mb-2">
                                    <span class="font-mono bg-gray-200 px-2 py-1 rounded text-xs">${doc.filename}</span>
                                </p>
                                <div class="flex items-center space-x-4 text-xs text-gray-500">
                                    <span>${doc.created_at}</span>
                                    <span>${statusIcon} ${statusText}</span>
                                </div>
                            </div>
                            <div class="ml-4">
                                ${doc.file_exists ?
                                    `<a href="${downloadUrl}"
                                       class="px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded transition-colors">
                                        Download
                                    </a>` :
                                    `<span class="px-3 py-2 bg-gray-300 text-gray-500 text-sm rounded cursor-not-allowed">
                                        Tidak Tersedia
                                    </span>`
                                }
                            </div>
                        </div>
                    </div>
                `;
            });

            if (html === '') {
                html = '<div class="text-center py-8 text-gray-500">Belum ada dokumen yang di-generate</div>';
            }

            $('#documentsContent').html(html);
        } else {
            $('#documentsContent').html('<div class="text-center py-8 text-gray-500">Belum ada dokumen yang di-generate</div>');
        }
    };

    // Close documents modal
    window.closeDocumentsModal = function() {
        $('#documentsModal').addClass('hidden').removeClass('flex');
    };

    // Refresh table function
    window.refreshTable = function() {
        table.ajax.reload(null, false);
    };

    // Close modal when clicking outside
    $(document).on('click', function(e) {
        if ($(e.target).is('#documentsModal')) {
            closeDocumentsModal();
        }
    });
});
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Documents\code\mutasites\resources\views/documents/list.blade.php ENDPATH**/ ?>