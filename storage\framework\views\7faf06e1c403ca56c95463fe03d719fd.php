<?php $__env->startSection('title', 'Daftar Dokumen Pegawai'); ?>

<?php $__env->startSection('content'); ?>
<div class="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50">
    <!-- Header Section -->
    <div class="bg-white shadow-sm border-b border-gray-100">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">📋 Daftar Dokumen Pegawai</h1>
                    <p class="mt-2 text-gray-600">Kelola dan pantau dokumen yang telah di-generate untuk setiap pegawai</p>
                </div>
                <div class="flex flex-col sm:flex-row gap-3">
                    <a href="<?php echo e(route('documents.index')); ?>"
                       class="inline-flex items-center justify-center px-4 py-2 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white text-sm font-medium rounded-lg transition-all duration-200 transform hover:scale-105 shadow-lg">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        Generate Dokumen Baru
                    </a>
                    <button onclick="refreshTable()"
                            class="inline-flex items-center justify-center px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 text-sm font-medium rounded-lg transition-colors duration-200">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                        </svg>
                        Refresh
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                <div class="flex items-center">
                    <div class="p-3 bg-blue-100 rounded-full">
                        <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Total Pegawai</p>
                        <p class="text-2xl font-bold text-gray-900" id="totalPegawai">-</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                <div class="flex items-center">
                    <div class="p-3 bg-green-100 rounded-full">
                        <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Total Dokumen</p>
                        <p class="text-2xl font-bold text-gray-900" id="totalDokumen">-</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                <div class="flex items-center">
                    <div class="p-3 bg-purple-100 rounded-full">
                        <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Hari Ini</p>
                        <p class="text-2xl font-bold text-gray-900" id="dokumenHariIni">-</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- DataTable Card -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
            <div class="px-6 py-4 bg-gradient-to-r from-gray-50 to-gray-100 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-900">Data Pegawai & Dokumen</h2>
                <p class="text-sm text-gray-600 mt-1">Daftar pegawai yang memiliki dokumen yang telah di-generate</p>
            </div>

            <div class="p-6">
                <table id="pegawaiDocumentsTable" class="w-full">
                    <thead>
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                NIP
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Nama Pegawai
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Golongan
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Jabatan
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Unit Kerja
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Jumlah Dokumen
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Aksi
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <!-- Data will be loaded via DataTables -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Loading Modal -->
<div id="loadingModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden items-center justify-center z-50">
    <div class="bg-white rounded-lg p-6 max-w-sm mx-auto">
        <div class="flex items-center">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mr-3"></div>
            <span class="text-gray-700">Memuat data...</span>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<!-- DataTables CSS -->
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.13.6/css/dataTables.tailwindcss.min.css">
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/responsive/2.5.0/css/responsive.dataTables.min.css">
<!-- SweetAlert2 CSS -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<!-- jQuery -->
<script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
<!-- SweetAlert2 JS -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<!-- DataTables JS -->
<script type="text/javascript" src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
<script type="text/javascript" src="https://cdn.datatables.net/1.13.6/js/dataTables.tailwindcss.min.js"></script>
<script type="text/javascript" src="https://cdn.datatables.net/responsive/2.5.0/js/dataTables.responsive.min.js"></script>

<script>
$(document).ready(function() {
    // Show loading modal
    function showLoading() {
        $('#loadingModal').removeClass('hidden').addClass('flex');
    }
    
    // Hide loading modal
    function hideLoading() {
        $('#loadingModal').addClass('hidden').removeClass('flex');
    }

    // Initialize DataTable
    const table = $('#pegawaiDocumentsTable').DataTable({
        processing: true,
        serverSide: true,
        responsive: true,
        pageLength: 25,
        language: {
            processing: '<div class="flex items-center justify-center"><div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mr-2"></div>Memuat data...</div>',
            search: 'Cari:',
            lengthMenu: 'Tampilkan _MENU_ data per halaman',
            info: 'Menampilkan _START_ sampai _END_ dari _TOTAL_ data',
            infoEmpty: 'Tidak ada data',
            infoFiltered: '(difilter dari _MAX_ total data)',
            paginate: {
                first: 'Pertama',
                last: 'Terakhir',
                next: 'Selanjutnya',
                previous: 'Sebelumnya'
            },
            emptyTable: 'Belum ada dokumen yang di-generate'
        },
        ajax: {
            url: '<?php echo e(route("documents.pegawai-documents")); ?>',
            type: 'GET',
            beforeSend: function() {
                showLoading();
            },
            complete: function(response) {
                hideLoading();
                updateStats(response.responseJSON);
            },
            error: function(xhr, error, thrown) {
                hideLoading();
                console.error('DataTables error:', error, thrown);

                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: 'Gagal memuat data pegawai. Silakan coba lagi.',
                    confirmButtonColor: '#EF4444'
                });
            }
        },
        columns: [
            { 
                data: 'nip',
                name: 'nip',
                className: 'font-mono text-sm'
            },
            { 
                data: 'nama',
                name: 'nama',
                className: 'font-medium'
            },
            { 
                data: 'golongan',
                name: 'golongan',
                className: 'text-center'
            },
            { 
                data: 'jabatan',
                name: 'jabatan',
                className: 'text-sm',
                render: function(data, type, row) {
                    if (data && data.length > 50) {
                        return '<span title="' + data + '">' + data.substring(0, 50) + '...</span>';
                    }
                    return data || '-';
                }
            },
            { 
                data: 'unit_kerja',
                name: 'unit_kerja',
                className: 'text-sm',
                render: function(data, type, row) {
                    if (data && data.length > 30) {
                        return '<span title="' + data + '">' + data.substring(0, 30) + '...</span>';
                    }
                    return data || '-';
                }
            },
            { 
                data: 'document_count',
                name: 'document_count',
                className: 'text-center',
                render: function(data, type, row) {
                    if (data > 0) {
                        return '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">' + data + ' dokumen</span>';
                    }
                    return '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">0 dokumen</span>';
                }
            },
            { 
                data: null,
                name: 'actions',
                orderable: false,
                searchable: false,
                className: 'text-center',
                render: function(data, type, row) {
                    if (row.document_count > 0) {
                        let dropdown = '<div class="relative inline-block text-left">';
                        dropdown += '<button type="button" class="inline-flex justify-center w-full rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" onclick="toggleDropdown(this)">';
                        dropdown += 'Lihat Dokumen <svg class="-mr-1 ml-2 h-5 w-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg>';
                        dropdown += '</button>';
                        dropdown += '<div class="dropdown-menu origin-top-right absolute right-0 mt-2 w-56 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 hidden z-10">';
                        dropdown += '<div class="py-1">';
                        
                        row.documents.forEach(function(doc) {
                            const downloadUrl = '<?php echo e(route("documents.download-document", ":filename")); ?>'.replace(':filename', doc.filename);
                            dropdown += '<a href="' + downloadUrl + '" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">';
                            dropdown += '<div class="flex items-center">';
                            dropdown += '<svg class="w-4 h-4 mr-2 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path></svg>';
                            dropdown += '<div>';
                            dropdown += '<div class="font-medium">' + doc.template_name + '</div>';
                            dropdown += '<div class="text-xs text-gray-500">' + doc.created_at + '</div>';
                            dropdown += '</div>';
                            dropdown += '</div>';
                            dropdown += '</a>';
                        });
                        
                        dropdown += '</div></div></div>';
                        return dropdown;
                    }
                    return '<span class="text-gray-400 text-sm">Tidak ada dokumen</span>';
                }
            }
        ],
        pageLength: 25,
        lengthMenu: [[10, 25, 50, 100], [10, 25, 50, 100]],
        language: {
            processing: "Memproses...",
            search: "Cari:",
            lengthMenu: "Tampilkan _MENU_ data per halaman",
            info: "Menampilkan _START_ sampai _END_ dari _TOTAL_ data",
            infoEmpty: "Menampilkan 0 sampai 0 dari 0 data",
            infoFiltered: "(disaring dari _MAX_ total data)",
            loadingRecords: "Memuat...",
            zeroRecords: "Tidak ada data yang ditemukan",
            emptyTable: "Tidak ada data tersedia",
            paginate: {
                first: "Pertama",
                previous: "Sebelumnya",
                next: "Selanjutnya",
                last: "Terakhir"
            }
        },
        order: [[1, 'asc']] // Sort by nama
    });

    // Handle dropdown toggle
    window.toggleDropdown = function(button) {
        const dropdown = button.nextElementSibling;
        const isHidden = dropdown.classList.contains('hidden');
        
        // Close all other dropdowns
        document.querySelectorAll('.dropdown-menu').forEach(menu => {
            menu.classList.add('hidden');
        });
        
        // Toggle current dropdown
        if (isHidden) {
            dropdown.classList.remove('hidden');
        }
    };

    // Close dropdown when clicking outside
    document.addEventListener('click', function(event) {
        if (!event.target.closest('.relative')) {
            document.querySelectorAll('.dropdown-menu').forEach(menu => {
                menu.classList.add('hidden');
            });
        }
    });

    // Update stats function
    function updateStats(data) {
        if (data && data.data) {
            const totalPegawai = data.recordsTotal || 0;
            let totalDokumen = 0;
            let dokumenHariIni = 0;
            const today = new Date().toISOString().split('T')[0];

            data.data.forEach(function(pegawai) {
                totalDokumen += pegawai.document_count || 0;

                if (pegawai.documents) {
                    pegawai.documents.forEach(function(doc) {
                        if (doc.created_at && doc.created_at.startsWith(today)) {
                            dokumenHariIni++;
                        }
                    });
                }
            });

            // Animate counter updates
            animateCounter('#totalPegawai', totalPegawai);
            animateCounter('#totalDokumen', totalDokumen);
            animateCounter('#dokumenHariIni', dokumenHariIni);
        }
    }

    // Animate counter function
    function animateCounter(selector, targetValue) {
        const element = $(selector);
        const currentValue = parseInt(element.text()) || 0;
        const increment = Math.ceil((targetValue - currentValue) / 20);

        if (currentValue !== targetValue) {
            element.text(Math.min(currentValue + increment, targetValue));
            setTimeout(() => animateCounter(selector, targetValue), 50);
        }
    }

    // Refresh table function
    window.refreshTable = function() {
        table.ajax.reload(null, false);
    };
});

// Global refresh function
function refreshTable() {
    if (typeof table !== 'undefined') {
        table.ajax.reload(null, false);
    }
}
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Documents\code\mutasites\resources\views/documents/list.blade.php ENDPATH**/ ?>