<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class AnalyzeTemplate extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'template:analyze {filename?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Analyze template file and show available variables';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $filename = $this->argument('filename');
        $templatesPath = storage_path('app/templates');

        if (!$filename) {
            // List all templates
            $this->info('Available templates:');
            $files = glob($templatesPath . '/*.docx');

            foreach ($files as $file) {
                $name = basename($file);
                if (strpos($name, '~$') !== 0) {
                    $this->line("- {$name}");
                }
            }

            $this->info("\nUse: php artisan template:analyze <filename>");
            return;
        }

        $templatePath = $templatesPath . '/' . $filename;

        if (!file_exists($templatePath)) {
            $this->error("Template file not found: {$filename}");
            return;
        }

        try {
            $documentService = app(\App\Services\DocumentGeneratorService::class);
            $variables = $documentService->getTemplateVariables($filename);

            $this->info("Template: {$filename}");
            $this->info("Variables found: " . count($variables));

            if (empty($variables)) {
                $this->warn("No variables found in template");
            } else {
                $this->info("\nVariables:");
                foreach ($variables as $variable) {
                    $this->line("- {{{$variable}}}");
                }
            }

        } catch (\Exception $e) {
            $this->error("Error analyzing template: " . $e->getMessage());
        }
    }
}
