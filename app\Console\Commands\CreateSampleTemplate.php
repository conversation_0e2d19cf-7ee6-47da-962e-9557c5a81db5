<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use PhpOffice\PhpWord\PhpWord;
use PhpOffice\PhpWord\IOFactory;

class CreateSampleTemplate extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'template:create-sample';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create sample template with placeholders';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $phpWord = new PhpWord();
        
        // Add section
        $section = $phpWord->addSection();
        
        // Header
        $section->addText(
            'KEMENTERIAN AGAMA REPUBLIK INDONESIA',
            ['bold' => true, 'size' => 14],
            ['alignment' => 'center']
        );
        
        $section->addText(
            'KANTOR WILAYAH KEMENTERIAN AGAMA PROVINSI NUSA TENGGARA BARAT',
            ['bold' => true, 'size' => 12],
            ['alignment' => 'center']
        );
        
        $section->addTextBreak(2);
        
        // Title
        $section->addText(
            'SURAT PERNYATAAN',
            ['bold' => true, 'size' => 14],
            ['alignment' => 'center']
        );
        
        $section->addText(
            'TIDAK SEDANG MENJALANI PROSES HUKUMAN DISIPLIN',
            ['bold' => true, 'size' => 12],
            ['alignment' => 'center']
        );
        
        $section->addTextBreak(2);
        
        // Content
        $section->addText('Yang bertanda tangan di bawah ini:');
        $section->addTextBreak();
        
        $section->addText('Nama                    : ${NAMA}');
        $section->addText('NIP                     : ${NIP}');
        $section->addText('Golongan/Ruang          : ${GOLONGAN}');
        $section->addText('Jabatan                 : ${JABATAN}');
        $section->addText('Unit Kerja              : ${UNIT_KERJA}');
        
        $section->addTextBreak();
        
        $section->addText(
            'Dengan ini menyatakan dengan sesungguhnya bahwa saya TIDAK SEDANG MENJALANI PROSES HUKUMAN DISIPLIN ' .
            'di lingkungan Kementerian Agama maupun instansi lain.'
        );
        
        $section->addTextBreak();
        
        $section->addText(
            'Demikian surat pernyataan ini saya buat dengan sebenar-benarnya dan penuh tanggung jawab. ' .
            'Apabila di kemudian hari ternyata pernyataan saya tidak benar, maka saya bersedia menerima ' .
            'sanksi sesuai dengan ketentuan peraturan perundang-undangan yang berlaku.'
        );
        
        $section->addTextBreak(2);
        
        // Signature
        $section->addText(
            'Mataram, ${TANGGAL}',
            [],
            ['alignment' => 'right']
        );
        
        $section->addText(
            'Yang membuat pernyataan,',
            [],
            ['alignment' => 'right']
        );
        
        $section->addTextBreak(3);
        
        $section->addText(
            '${NAMA}',
            ['bold' => true],
            ['alignment' => 'right']
        );
        
        $section->addText(
            'NIP. ${NIP}',
            [],
            ['alignment' => 'right']
        );
        
        // Save template
        $templatePath = storage_path('app/templates/SAMPLE_SURAT_PERNYATAAN.docx');
        
        $objWriter = IOFactory::createWriter($phpWord, 'Word2007');
        $objWriter->save($templatePath);
        
        $this->info("Sample template created: {$templatePath}");
        $this->info("Variables used: NAMA, NIP, GOLONGAN, JABATAN, UNIT_KERJA, TANGGAL");
    }
}
