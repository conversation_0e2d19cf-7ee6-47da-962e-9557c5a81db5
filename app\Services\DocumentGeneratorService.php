<?php

namespace App\Services;

use PhpOffice\PhpWord\TemplateProcessor;
use PhpOffice\PhpWord\IOFactory;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Exception;

class DocumentGeneratorService
{
    protected $templatesPath;
    protected $outputPath;

    public function __construct()
    {
        $this->templatesPath = storage_path('app/templates');
        $this->outputPath = storage_path('app/generated');
        
        // Create output directory if it doesn't exist
        if (!file_exists($this->outputPath)) {
            mkdir($this->outputPath, 0755, true);
        }
    }

    /**
     * Generate document from template with data replacement
     */
    public function generateDocument($templateName, $data, $outputFileName = null)
    {
        try {
            $templatePath = $this->templatesPath . '/' . $templateName;
            
            if (!file_exists($templatePath)) {
                throw new Exception("Template file not found: {$templateName}");
            }

            Log::info('DocumentGenerator: Starting document generation', [
                'template' => $templateName,
                'data_keys' => array_keys($data),
                'template_path' => $templatePath
            ]);

            // Load template
            $templateProcessor = new TemplateProcessor($templatePath);
            
            // Get all variables in template
            $variables = $templateProcessor->getVariables();
            Log::info('DocumentGenerator: Template variables found', [
                'variables' => $variables
            ]);

            // Replace variables with data
            foreach ($data as $key => $value) {
                if (in_array($key, $variables)) {
                    $templateProcessor->setValue($key, $value);
                    Log::info("DocumentGenerator: Replaced variable", [
                        'key' => $key,
                        'value' => $value
                    ]);
                }
            }

            // Generate output filename if not provided
            if (!$outputFileName) {
                $outputFileName = pathinfo($templateName, PATHINFO_FILENAME) . '_' . date('Y-m-d_H-i-s') . '.docx';
            }

            $outputPath = $this->outputPath . '/' . $outputFileName;

            // Save the document
            $templateProcessor->saveAs($outputPath);

            Log::info('DocumentGenerator: Document generated successfully', [
                'output_path' => $outputPath,
                'file_size' => filesize($outputPath)
            ]);

            return [
                'success' => true,
                'file_path' => $outputPath,
                'file_name' => $outputFileName,
                'download_url' => route('document.download', ['filename' => $outputFileName])
            ];

        } catch (Exception $e) {
            Log::error('DocumentGenerator: Generation failed', [
                'template' => $templateName,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => 'Document generation failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Get list of available templates
     */
    public function getAvailableTemplates()
    {
        try {
            $templates = [];
            $files = glob($this->templatesPath . '/*.docx');
            
            foreach ($files as $file) {
                $filename = basename($file);
                
                // Skip temporary files
                if (strpos($filename, '~$') === 0) {
                    continue;
                }
                
                $templates[] = [
                    'filename' => $filename,
                    'name' => pathinfo($filename, PATHINFO_FILENAME),
                    'size' => filesize($file),
                    'modified' => date('Y-m-d H:i:s', filemtime($file))
                ];
            }

            return $templates;
        } catch (Exception $e) {
            Log::error('DocumentGenerator: Failed to get templates', [
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Get template variables (placeholders)
     */
    public function getTemplateVariables($templateName)
    {
        try {
            $templatePath = $this->templatesPath . '/' . $templateName;
            
            if (!file_exists($templatePath)) {
                throw new Exception("Template file not found: {$templateName}");
            }

            $templateProcessor = new TemplateProcessor($templatePath);
            $variables = $templateProcessor->getVariables();

            Log::info('DocumentGenerator: Retrieved template variables', [
                'template' => $templateName,
                'variables' => $variables
            ]);

            return $variables;
        } catch (Exception $e) {
            Log::error('DocumentGenerator: Failed to get template variables', [
                'template' => $templateName,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Clean up old generated files
     */
    public function cleanupOldFiles($olderThanHours = 24)
    {
        try {
            $files = glob($this->outputPath . '/*.docx');
            $cutoffTime = time() - ($olderThanHours * 3600);
            $deletedCount = 0;

            foreach ($files as $file) {
                if (filemtime($file) < $cutoffTime) {
                    unlink($file);
                    $deletedCount++;
                }
            }

            Log::info('DocumentGenerator: Cleanup completed', [
                'deleted_files' => $deletedCount,
                'cutoff_hours' => $olderThanHours
            ]);

            return $deletedCount;
        } catch (Exception $e) {
            Log::error('DocumentGenerator: Cleanup failed', [
                'error' => $e->getMessage()
            ]);
            return 0;
        }
    }
}
