<?php

namespace App\Services;

use PhpOffice\PhpWord\TemplateProcessor;
use PhpOffice\PhpWord\IOFactory;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Exception;

class DocumentGeneratorService
{
    protected $templatesPath;
    protected $outputPath;

    public function __construct()
    {
        $this->templatesPath = storage_path('app/templates');
        $this->outputPath = storage_path('app/generated');
        
        // Create output directory if it doesn't exist
        if (!file_exists($this->outputPath)) {
            mkdir($this->outputPath, 0755, true);
        }
    }

    /**
     * Generate document from template with data replacement
     */
    public function generateDocument($templateName, $data, $outputFileName = null)
    {
        try {
            $templatePath = $this->templatesPath . '/' . $templateName;
            
            if (!file_exists($templatePath)) {
                throw new Exception("Template file not found: {$templateName}");
            }

            Log::info('DocumentGenerator: Starting document generation', [
                'template' => $templateName,
                'data_keys' => array_keys($data),
                'template_path' => $templatePath
            ]);

            // Load template
            $templateProcessor = new TemplateProcessor($templatePath);

            // Get all variables in template
            $variables = $templateProcessor->getVariables();

            // If no standard variables found, try custom format
            if (empty($variables)) {
                $variables = $this->extractCustomVariables($templatePath);

                // For custom format, we need to replace manually
                if (!empty($variables)) {
                    return $this->generateDocumentWithCustomFormat($templatePath, $data, $outputFileName);
                }
            }

            Log::info('DocumentGenerator: Template variables found', [
                'variables' => $variables
            ]);

            // Replace variables with data (standard PHPWord format)
            foreach ($data as $key => $value) {
                if (in_array($key, $variables)) {
                    $templateProcessor->setValue($key, $value);
                    Log::info("DocumentGenerator: Replaced variable", [
                        'key' => $key,
                        'value' => $value
                    ]);
                }
            }

            // Generate output filename if not provided
            if (!$outputFileName) {
                $outputFileName = pathinfo($templateName, PATHINFO_FILENAME) . '_' . date('Y-m-d_H-i-s') . '.docx';
            }

            $outputPath = $this->outputPath . '/' . $outputFileName;

            // Save the document
            $templateProcessor->saveAs($outputPath);

            Log::info('DocumentGenerator: Document generated successfully', [
                'output_path' => $outputPath,
                'file_size' => filesize($outputPath)
            ]);

            return [
                'success' => true,
                'file_path' => $outputPath,
                'file_name' => $outputFileName,
                'download_url' => route('document.download', ['filename' => $outputFileName])
            ];

        } catch (Exception $e) {
            Log::error('DocumentGenerator: Generation failed', [
                'template' => $templateName,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => 'Document generation failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Get list of available templates
     */
    public function getAvailableTemplates()
    {
        try {
            $templates = [];
            $files = glob($this->templatesPath . '/*.docx');
            
            foreach ($files as $file) {
                $filename = basename($file);
                
                // Skip temporary files
                if (strpos($filename, '~$') === 0) {
                    continue;
                }
                
                $templates[] = [
                    'filename' => $filename,
                    'name' => pathinfo($filename, PATHINFO_FILENAME),
                    'size' => filesize($file),
                    'modified' => date('Y-m-d H:i:s', filemtime($file))
                ];
            }

            return $templates;
        } catch (Exception $e) {
            Log::error('DocumentGenerator: Failed to get templates', [
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Get template variables (placeholders)
     */
    public function getTemplateVariables($templateName)
    {
        try {
            $templatePath = $this->templatesPath . '/' . $templateName;

            if (!file_exists($templatePath)) {
                throw new Exception("Template file not found: {$templateName}");
            }

            // Try PHPWord standard format first
            $templateProcessor = new TemplateProcessor($templatePath);
            $variables = $templateProcessor->getVariables();

            // If no variables found, try to extract custom format {{variable}}
            if (empty($variables)) {
                $variables = $this->extractCustomVariables($templatePath);
            }

            Log::info('DocumentGenerator: Retrieved template variables', [
                'template' => $templateName,
                'variables' => $variables,
                'count' => count($variables)
            ]);

            return $variables;
        } catch (Exception $e) {
            Log::error('DocumentGenerator: Failed to get template variables', [
                'template' => $templateName,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Extract variables from custom format {{variable}}
     */
    private function extractCustomVariables($templatePath)
    {
        try {
            // Read the document content
            $zip = new \ZipArchive();
            if ($zip->open($templatePath) === TRUE) {
                $content = $zip->getFromName('word/document.xml');
                $zip->close();

                if ($content) {
                    // Remove XML tags to get clean text
                    $cleanContent = strip_tags($content);

                    // Find all {{variable}} patterns in clean text
                    preg_match_all('/\{\{([a-zA-Z0-9_]+)\}\}/', $cleanContent, $matches);

                    if (!empty($matches[1])) {
                        $variables = array_unique($matches[1]);

                        // Filter out any remaining XML artifacts
                        $cleanVariables = [];
                        foreach ($variables as $var) {
                            if (preg_match('/^[a-zA-Z0-9_]+$/', $var)) {
                                $cleanVariables[] = $var;
                            }
                        }

                        Log::info('DocumentGenerator: Found custom variables', [
                            'variables' => $cleanVariables
                        ]);
                        return $cleanVariables;
                    }
                }
            }

            return [];
        } catch (Exception $e) {
            Log::error('DocumentGenerator: Failed to extract custom variables', [
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Generate document with custom format {{variable}}
     */
    private function generateDocumentWithCustomFormat($templatePath, $data, $outputFileName = null)
    {
        try {
            // Generate output filename if not provided
            if (!$outputFileName) {
                $templateName = basename($templatePath);
                $outputFileName = pathinfo($templateName, PATHINFO_FILENAME) . '_' . date('Y-m-d_H-i-s') . '.docx';
            }

            $outputPath = $this->outputPath . '/' . $outputFileName;

            // Copy template to output location
            copy($templatePath, $outputPath);

            // Open the copied file and replace variables
            $zip = new \ZipArchive();
            if ($zip->open($outputPath) === TRUE) {
                // Read document content
                $content = $zip->getFromName('word/document.xml');

                if ($content) {
                    // First, try to fix broken placeholders in XML
                    $content = $this->fixBrokenPlaceholders($content);

                    // Replace all {{variable}} with actual data
                    foreach ($data as $key => $value) {
                        $placeholder = '{{' . $key . '}}';
                        $escapedValue = htmlspecialchars($value, ENT_XML1, 'UTF-8');

                        // Count occurrences before replacement
                        $beforeCount = substr_count($content, $placeholder);

                        $content = str_replace($placeholder, $escapedValue, $content);

                        // Count occurrences after replacement
                        $afterCount = substr_count($content, $placeholder);
                        $replacedCount = $beforeCount - $afterCount;

                        Log::info("DocumentGenerator: Replaced custom variable", [
                            'placeholder' => $placeholder,
                            'value' => $value,
                            'found_count' => $beforeCount,
                            'replaced_count' => $replacedCount
                        ]);
                    }

                    // Write back the modified content
                    $zip->deleteName('word/document.xml');
                    $zip->addFromString('word/document.xml', $content);
                }

                $zip->close();

                Log::info('DocumentGenerator: Custom format document generated successfully', [
                    'output_path' => $outputPath,
                    'file_size' => filesize($outputPath)
                ]);

                return [
                    'success' => true,
                    'file_path' => $outputPath,
                    'file_name' => $outputFileName,
                    'download_url' => route('documents.download', ['filename' => $outputFileName])
                ];
            } else {
                throw new Exception('Failed to open document for custom format replacement');
            }

        } catch (Exception $e) {
            Log::error('DocumentGenerator: Custom format generation failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => 'Custom format document generation failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Fix broken placeholders in Word XML
     * Word sometimes splits {{variable}} across multiple XML nodes
     */
    private function fixBrokenPlaceholders($content)
    {
        // Pattern to match broken placeholders like {{var<w:t>iable}}
        // This regex finds {{ followed by any characters and XML tags until }}
        $pattern = '/\{\{([^}]*(?:<[^>]*>[^}]*)*)\}\}/';

        $content = preg_replace_callback($pattern, function($matches) {
            $placeholder = $matches[0];
            $variable = $matches[1];

            // Remove XML tags from variable name to get clean variable
            $cleanVariable = strip_tags($variable);

            // Return clean placeholder
            $cleanPlaceholder = '{{' . $cleanVariable . '}}';

            Log::info("DocumentGenerator: Fixed broken placeholder", [
                'original' => $placeholder,
                'fixed' => $cleanPlaceholder
            ]);

            return $cleanPlaceholder;
        }, $content);

        return $content;
    }

    /**
     * Clean up old generated files
     */
    public function cleanupOldFiles($olderThanHours = 24)
    {
        try {
            $files = glob($this->outputPath . '/*.docx');
            $cutoffTime = time() - ($olderThanHours * 3600);
            $deletedCount = 0;

            foreach ($files as $file) {
                if (filemtime($file) < $cutoffTime) {
                    unlink($file);
                    $deletedCount++;
                }
            }

            Log::info('DocumentGenerator: Cleanup completed', [
                'deleted_files' => $deletedCount,
                'cutoff_hours' => $olderThanHours
            ]);

            return $deletedCount;
        } catch (Exception $e) {
            Log::error('DocumentGenerator: Cleanup failed', [
                'error' => $e->getMessage()
            ]);
            return 0;
        }
    }
}
