<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\GeneratedDocument;
use App\Services\PegawaiApiService;

class MigrateExistingDocuments extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'documents:migrate-existing {--force : Force migration even if records exist}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Migrate existing generated documents from file system to database';

    protected $pegawaiApiService;

    public function __construct(PegawaiApiService $pegawaiApiService)
    {
        parent::__construct();
        $this->pegawaiApiService = $pegawaiApiService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting migration of existing documents...');

        // Check if there are already records
        $existingCount = GeneratedDocument::count();
        if ($existingCount > 0 && !$this->option('force')) {
            $this->warn("Found {$existingCount} existing records in database.");
            if (!$this->confirm('Do you want to continue? This might create duplicates.')) {
                $this->info('Migration cancelled.');
                return;
            }
        }

        $generatedPath = storage_path('app/generated');

        if (!is_dir($generatedPath)) {
            $this->error("Generated documents directory not found: {$generatedPath}");
            return;
        }

        $files = glob($generatedPath . '/*.docx');
        $this->info("Found " . count($files) . " document files to process.");

        $processed = 0;
        $skipped = 0;
        $errors = 0;

        foreach ($files as $file) {
            $filename = basename($file);

            // Skip temporary files
            if (strpos($filename, '~$') === 0) {
                continue;
            }

            try {
                $result = $this->processFile($file);
                if ($result) {
                    $processed++;
                    $this->line("✓ Processed: {$filename}");
                } else {
                    $skipped++;
                    $this->line("- Skipped: {$filename}");
                }
            } catch (\Exception $e) {
                $errors++;
                $this->error("✗ Error processing {$filename}: " . $e->getMessage());
            }
        }

        $this->info("\nMigration completed!");
        $this->table(['Status', 'Count'], [
            ['Processed', $processed],
            ['Skipped', $skipped],
            ['Errors', $errors],
            ['Total Files', count($files)]
        ]);
    }

    private function processFile($filePath)
    {
        $filename = basename($filePath);

        // Check if already exists in database
        if (GeneratedDocument::where('generated_filename', $filename)->exists()) {
            return false; // Skip existing
        }

        // Extract NIP from document content
        $nip = $this->extractNipFromDocument($filePath);
        if (!$nip) {
            $this->warn("Could not extract NIP from: {$filename}");
            return false;
        }

        // Get pegawai data from API
        $pegawaiResult = $this->pegawaiApiService->getPegawaiByNip($nip);
        if (!$pegawaiResult['success']) {
            $this->warn("Could not get pegawai data for NIP {$nip}");
            return false;
        }

        $pegawaiData = $pegawaiResult['data'];

        // Extract template name from filename
        $templateName = $this->extractTemplateNameFromFilename($filename);

        // Create database record
        GeneratedDocument::create([
            'pegawai_nip' => $pegawaiData['nip'] ?? '',
            'pegawai_nama' => $pegawaiData['nama'] ?? '',
            'pegawai_golongan' => $pegawaiData['golongan'] ?? '',
            'pegawai_jabatan' => $pegawaiData['jabatan'] ?? '',
            'pegawai_unit_kerja' => $pegawaiData['unit_kerja'] ?? '',

            'pejabat_nip' => null, // We can't extract this from existing files
            'pejabat_nama' => null,
            'pejabat_golongan' => null,
            'pejabat_jabatan' => null,
            'pejabat_unit_kerja' => null,

            'template_name' => $templateName,
            'template_filename' => $templateName . '.docx',
            'generated_filename' => $filename,
            'file_path' => $filePath,
            'file_size' => filesize($filePath),

            'generated_by' => 'migration',
            'generated_at' => date('Y-m-d H:i:s', filemtime($filePath)),
            'template_variables' => null
        ]);

        return true;
    }

    private function extractNipFromDocument($filePath)
    {
        try {
            $zip = new \ZipArchive();
            if ($zip->open($filePath) === TRUE) {
                $content = $zip->getFromName('word/document.xml');
                $zip->close();

                if ($content) {
                    // Look for all NIP patterns (18 digits) in the document
                    if (preg_match_all('/\b(\d{18})\b/', $content, $matches)) {
                        $nips = $matches[1];

                        // If we have multiple NIPs, try to determine which is pegawai
                        // Usually pegawai NIP appears after pejabat NIP in the document structure
                        // Or we can check the context around the NIP
                        foreach ($nips as $nip) {
                            // Try to get pegawai data for each NIP to see which one is valid
                            $pegawaiResult = $this->pegawaiApiService->getPegawaiByNip($nip);
                            if ($pegawaiResult['success']) {
                                $pegawaiData = $pegawaiResult['data'];
                                // Check if this looks like a regular pegawai (not pejabat)
                                // We can use jabatan or other criteria to distinguish
                                $jabatan = strtolower($pegawaiData['jabatan'] ?? '');

                                // Skip if this looks like a pejabat (kepala, direktur, etc.)
                                if (strpos($jabatan, 'kepala') === false &&
                                    strpos($jabatan, 'direktur') === false &&
                                    strpos($jabatan, 'sekretaris') === false) {
                                    return $nip; // This is likely the pegawai
                                }
                            }
                        }

                        // If no clear pegawai found, return the last NIP (usually pegawai in our template)
                        return end($nips);
                    }
                }
            }
        } catch (\Exception $e) {
            // Fallback: try to extract from filename
            if (preg_match('/(\d{18})/', basename($filePath), $matches)) {
                return $matches[1];
            }
        }

        return null;
    }

    private function extractTemplateNameFromFilename($filename)
    {
        // Remove timestamp suffix and .docx extension
        return preg_replace('/_\d{4}-\d{2}-\d{2}_\d{2}-\d{2}-\d{2}\.docx$/', '', $filename);
    }
}
