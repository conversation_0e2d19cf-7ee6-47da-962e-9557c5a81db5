<?php

namespace App\Http\Controllers\Templates;

use Illuminate\Http\Request;

class PernyataanHukumanDisiplinController extends BaseTemplateController
{
    protected $templateName = 'KEMENAG_PERNYATAAN TIDAK SEDANG MENALANI PROSES HUKUMAN DISIPLIN.docx';
    protected $templateDisplayName = 'Pernyataan Tidak Sedang Menjalani Proses Hukuman Disiplin';
    
    protected $requiredFields = [
        // Tidak ada field tambahan yang required untuk template ini
    ];
    
    protected $optionalFields = [
        'keperluan' => 'Keperluan surat pernyataan'
    ];

    /**
     * Prepare template-specific data
     */
    protected function prepareTemplateData($pegawaiData, $pejabatData, $requestData)
    {
        // Extract jabatan sebelum kata "pada"
        $jabatanPegawai = $this->extractJabatanSebelumPada($pegawaiData['jabatan'] ?? '');
        $jabatanPejabat = $this->extractJabatanSebelumPada($pejabatData['jabatan'] ?? '');

        return [
            // Data Pegawai
            'nama_pegawai' => $pegawaiData['nama'] ?? '',
            'nip_pegawai' => $pegawaiData['nip'] ?? '',
            'golongan_pegawai' => $pegawaiData['golongan'] ?? '',
            'jabatan_pegawai' => $jabatanPegawai,
            'unit_kerja_pegawai' => $pegawaiData['unit_kerja'] ?? '',
            
            // Data Pejabat
            'nama_pejabat' => $pejabatData['nama'] ?? '',
            'nip_pejabat' => $pejabatData['nip'] ?? '',
            'golongan_pejabat' => $pejabatData['golongan'] ?? '',
            'jabatan_pejabat' => $jabatanPejabat,
            'unit_kerja_pejabat' => $pejabatData['unit_kerja'] ?? '',
            
            // Data Tambahan
            'tanggal_surat' => date('d F Y'),
            'tempat_surat' => 'Mataram',
            'keperluan' => $requestData['keperluan'] ?? 'keperluan administrasi',
            
            // Format tanggal Indonesia
            'hari_ini' => $this->formatTanggalIndonesia(date('Y-m-d')),
            'bulan_tahun' => $this->formatBulanTahun(date('Y-m-d'))
        ];
    }

    /**
     * Extract jabatan sebelum kata "pada"
     */
    private function extractJabatanSebelumPada($jabatan)
    {
        if (empty($jabatan)) {
            return '';
        }

        $posisiPada = stripos($jabatan, ' pada ');
        if ($posisiPada !== false) {
            return trim(substr($jabatan, 0, $posisiPada));
        }

        return $jabatan;
    }

    /**
     * Format tanggal ke bahasa Indonesia
     */
    private function formatTanggalIndonesia($tanggal)
    {
        $bulan = [
            1 => 'Januari', 2 => 'Februari', 3 => 'Maret', 4 => 'April',
            5 => 'Mei', 6 => 'Juni', 7 => 'Juli', 8 => 'Agustus',
            9 => 'September', 10 => 'Oktober', 11 => 'November', 12 => 'Desember'
        ];

        $timestamp = strtotime($tanggal);
        $hari = date('d', $timestamp);
        $bulanNama = $bulan[date('n', $timestamp)];
        $tahun = date('Y', $timestamp);

        return $hari . ' ' . $bulanNama . ' ' . $tahun;
    }

    /**
     * Format bulan tahun untuk keperluan tertentu
     */
    private function formatBulanTahun($tanggal)
    {
        $bulan = [
            1 => 'Januari', 2 => 'Februari', 3 => 'Maret', 4 => 'April',
            5 => 'Mei', 6 => 'Juni', 7 => 'Juli', 8 => 'Agustus',
            9 => 'September', 10 => 'Oktober', 11 => 'November', 12 => 'Desember'
        ];

        $timestamp = strtotime($tanggal);
        $bulanNama = $bulan[date('n', $timestamp)];
        $tahun = date('Y', $timestamp);

        return $bulanNama . ' ' . $tahun;
    }

    /**
     * Custom validation for this template
     */
    protected function validateRequest(Request $request)
    {
        $rules = [
            'nip_pegawai' => 'required|string|size:18',
            'nip_pejabat' => 'required|string|size:18',
            'keperluan' => 'nullable|string|max:500'
        ];

        $request->validate($rules);
    }

    /**
     * Get template-specific view name
     */
    protected function getTemplateViewName()
    {
        return 'pernyataan-hukuman-disiplin';
    }
}
