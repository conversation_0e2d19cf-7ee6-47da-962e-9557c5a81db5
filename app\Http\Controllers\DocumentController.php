<?php

namespace App\Http\Controllers;

use App\Services\DocumentGeneratorService;
use App\Services\PegawaiApiService;
use App\Models\GeneratedDocument;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Response;

class DocumentController extends Controller
{
    protected $documentGenerator;
    protected $pegawaiApiService;

    public function __construct(DocumentGeneratorService $documentGenerator, PegawaiApiService $pegawaiApiService)
    {
        $this->documentGenerator = $documentGenerator;
        $this->pegawaiApiService = $pegawaiApiService;
    }

    /**
     * Show document generation form
     */
    public function index()
    {
        $templates = $this->documentGenerator->getAvailableTemplates();

        return view('documents.index', compact('templates'));
    }

    /**
     * Show document list per person
     */
    public function list()
    {
        return view('documents.list');
    }

    /**
     * Get pegawai data with their generated documents for DataTable
     */
    public function getPegawaiDocuments(Request $request)
    {
        try {
            // Get pagination parameters
            $start = intval($request->start ?? 0);
            $length = intval($request->length ?? 25);
            $search = $request->search['value'] ?? null;

            // Get unique pegawai from generated documents with search
            $query = GeneratedDocument::select([
                'pegawai_nip',
                'pegawai_nama',
                'pegawai_golongan',
                'pegawai_jabatan',
                'pegawai_unit_kerja'
            ])
            ->selectRaw('COUNT(*) as document_count')
            ->groupBy([
                'pegawai_nip',
                'pegawai_nama',
                'pegawai_golongan',
                'pegawai_jabatan',
                'pegawai_unit_kerja'
            ]);

            // Apply search filter
            if (!empty($search)) {
                $query->where(function($q) use ($search) {
                    $q->where('pegawai_nip', 'like', "%{$search}%")
                      ->orWhere('pegawai_nama', 'like', "%{$search}%")
                      ->orWhere('pegawai_jabatan', 'like', "%{$search}%");
                });
            }

            // Get total count before pagination
            $totalRecords = $query->count();

            // Apply pagination
            $pegawaiData = $query->orderBy('pegawai_nama')
                                ->offset($start)
                                ->limit($length)
                                ->get();

            Log::info('DocumentController: DataTable query results', [
                'total_records' => $totalRecords,
                'returned_count' => $pegawaiData->count(),
                'search' => $search,
                'start' => $start,
                'length' => $length
            ]);

            // Process each pegawai and get their documents
            $processedData = [];
            foreach ($pegawaiData as $pegawai) {
                $documents = GeneratedDocument::forPegawai($pegawai->pegawai_nip)
                    ->latest()
                    ->get()
                    ->map(function($doc) {
                        return [
                            'filename' => $doc->generated_filename,
                            'filepath' => $doc->file_path,
                            'created_at' => $doc->generated_at->format('Y-m-d H:i:s'),
                            'size' => $doc->file_size,
                            'template_name' => $doc->template_name,
                            'file_exists' => $doc->fileExists()
                        ];
                    });

                $processedData[] = [
                    'nip' => $pegawai->pegawai_nip,
                    'nama' => $pegawai->pegawai_nama,
                    'golongan' => $pegawai->pegawai_golongan,
                    'jabatan' => $pegawai->pegawai_jabatan,
                    'unit_kerja' => $pegawai->pegawai_unit_kerja,
                    'documents' => $documents,
                    'document_count' => $pegawai->document_count
                ];
            }

            return response()->json([
                'draw' => intval($request->draw),
                'recordsTotal' => $totalRecords,
                'recordsFiltered' => $totalRecords,
                'data' => $processedData
            ]);

        } catch (\Exception $e) {
            Log::error('DocumentController: Error getting pegawai documents', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'draw' => intval($request->draw),
                'recordsTotal' => 0,
                'recordsFiltered' => 0,
                'data' => [],
                'error' => 'Terjadi kesalahan: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Generate document
     */
    public function generate(Request $request)
    {
        $request->validate([
            'template' => 'required|string',
            'pejabat_nip' => 'required|string',
            'pegawai_nip' => 'required|string',
        ]);

        try {
            // Get pejabat data from API
            $pejabatResult = $this->pegawaiApiService->getPegawaiByNip($request->pejabat_nip);

            if (!$pejabatResult['success']) {
                return response()->json([
                    'success' => false,
                    'message' => 'Gagal mengambil data pejabat: ' . ($pejabatResult['message'] ?? 'Unknown error')
                ]);
            }

            // Get pegawai data from API
            $pegawaiResult = $this->pegawaiApiService->getPegawaiByNip($request->pegawai_nip);

            if (!$pegawaiResult['success']) {
                return response()->json([
                    'success' => false,
                    'message' => 'Gagal mengambil data pegawai: ' . ($pegawaiResult['message'] ?? 'Unknown error')
                ]);
            }

            $pejabatData = $pejabatResult['data'];
            $pegawaiData = $pegawaiResult['data'];

            Log::info('DocumentController: Data retrieved', [
                'pejabat_nip' => $request->pejabat_nip,
                'pejabat_nama' => $pejabatData['nama'] ?? 'N/A',
                'pegawai_nip' => $request->pegawai_nip,
                'pegawai_nama' => $pegawaiData['nama'] ?? 'N/A'
            ]);

            // Prepare data for template replacement
            $templateData = $this->prepareTemplateData($pejabatData, $pegawaiData, $request->all());

            // Generate document
            $result = $this->documentGenerator->generateDocument(
                $request->template,
                $templateData
            );

            if ($result['success']) {
                // Save to database
                $this->saveGeneratedDocumentRecord(
                    $pejabatData,
                    $pegawaiData,
                    $request->template,
                    $result['file_path'],
                    $result['file_name'],
                    $templateData,
                    $request
                );

                return response()->json([
                    'success' => true,
                    'message' => 'Dokumen berhasil dibuat',
                    'download_url' => $result['download_url'],
                    'file_name' => $result['file_name']
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => $result['message']
                ]);
            }

        } catch (\Exception $e) {
            Log::error('DocumentController: Generation failed', [
                'error' => $e->getMessage(),
                'template' => $request->template,
                'pejabat_nip' => $request->pejabat_nip ?? 'N/A',
                'pegawai_nip' => $request->pegawai_nip ?? 'N/A'
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat membuat dokumen: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Download generated document
     */
    public function download($filename)
    {
        $filePath = storage_path('app/generated/' . $filename);
        
        if (!file_exists($filePath)) {
            abort(404, 'File not found');
        }

        return Response::download($filePath, $filename, [
            'Content-Type' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        ]);
    }

    /**
     * Get template variables
     */
    public function getTemplateVariables(Request $request)
    {
        $request->validate([
            'template' => 'required|string'
        ]);

        $variables = $this->documentGenerator->getTemplateVariables($request->template);
        
        return response()->json([
            'success' => true,
            'variables' => $variables
        ]);
    }

    /**
     * Prepare data for template replacement
     */
    private function prepareTemplateData($pejabatData, $pegawaiData, $requestData)
    {
        // Prepare data with separate pejabat and pegawai information
        $data = [
            // Standard format (uppercase) - using pegawai data as default
            'NAMA' => $pegawaiData['nama'] ?? '',
            'NIP' => $pegawaiData['nip'] ?? '',
            'GOLONGAN' => $pegawaiData['golongan'] ?? '',
            'JABATAN' => $pegawaiData['jabatan'] ?? '',
            'UNIT_KERJA' => $pegawaiData['unit_kerja'] ?? '',
            'INDUK_UNIT' => $pegawaiData['induk_unit'] ?? '',
            'TMT_PENSIUN' => $pegawaiData['tmt_pensiun'] ?? '',

            // Pegawai data (yang dimaksud dalam surat)
            'namapegawai' => $pegawaiData['nama'] ?? '',
            'nippegawai' => $pegawaiData['nip'] ?? '',
            'pangkatgolpegawai' => $pegawaiData['golongan'] ?? '',
            'jabatanpegawai' => $this->extractJabatanSebelumPada($pegawaiData['jabatan'] ?? ''),
            'tempattugas' => $pegawaiData['unitkerja'] ?? $pegawaiData['unit_kerja'] ?? '',

            // Pejabat data (yang menandatangani surat)
            'namapejabat' => $pejabatData['nama'] ?? '',
            'nippejabat' => $pejabatData['nip'] ?? '',
            'pangkatgolpejabat' => $pejabatData['golongan'] ?? '',
            'jabatanpejabat' => $this->extractJabatanSebelumPada($pejabatData['jabatan'] ?? ''),
            'ukerpejabat' => $pejabatData['unit_kerja'] ?? '',
        ];

        // Add current date and time
        $data['TANGGAL'] = date('d F Y');
        $data['TANGGAL_INDO'] = $this->formatTanggalIndonesia(date('Y-m-d'));
        $data['HARI'] = $this->getHariIndonesia(date('w'));
        $data['BULAN'] = $this->getBulanIndonesia(date('n'));
        $data['TAHUN'] = date('Y');

        // Template specific date formats
        $data['dd-mm-yyyy'] = date('d-m-Y');

        // Office/location data (from form input or default)
        $data['kabkota'] = $requestData['kabkota'] ?? 'Mataram';
        $data['jln'] = $requestData['jln'] ?? 'Jl. Pendidikan No. 35';
        $data['telfon'] = $requestData['telfon'] ?? '(0370) 621974';
        $data['fax'] = $requestData['fax'] ?? '(0370) 621974';
        $data['email'] = $requestData['email'] ?? '<EMAIL>';

        // Document numbering (from form input or auto-generated)
        $data['nosrt'] = $requestData['nosrt'] ?? 'B-' . rand(1000, 9999);
        $data['blnno'] = $requestData['blnno'] ?? date('m');
        $data['thnno'] = $requestData['thnno'] ?? date('Y');

        // Add any additional data from request
        foreach ($requestData as $key => $value) {
            if (!in_array($key, ['template', 'pejabat_nip', 'pegawai_nip', '_token']) && is_string($value)) {
                $data[strtoupper($key)] = $value;
                $data[strtolower($key)] = $value;
            }
        }

        Log::info('DocumentController: Template data prepared', [
            'data_keys' => array_keys($data)
        ]);

        return $data;
    }

    /**
     * Find generated documents by NIP
     */
    private function findDocumentsByNip($nip, $generatedPath)
    {
        $documents = [];

        if (!is_dir($generatedPath)) {
            return $documents;
        }

        $files = glob($generatedPath . '/*.docx');

        foreach ($files as $file) {
            $filename = basename($file);

            // Skip temporary files
            if (strpos($filename, '~$') === 0) {
                continue;
            }

            // Check if this document belongs to the NIP
            // We'll check both filename and use a more sophisticated approach
            if ($this->isDocumentForNip($file, $nip)) {
                $documents[] = [
                    'filename' => $filename,
                    'filepath' => $file,
                    'created_at' => date('Y-m-d H:i:s', filemtime($file)),
                    'size' => filesize($file),
                    'template_name' => $this->extractTemplateNameFromFilename($filename)
                ];
            }
        }

        // Sort by creation time (newest first)
        usort($documents, function($a, $b) {
            return strtotime($b['created_at']) - strtotime($a['created_at']);
        });

        return $documents;
    }

    /**
     * Check if document belongs to specific NIP
     */
    private function isDocumentForNip($filePath, $nip)
    {
        $filename = basename($filePath);

        // Method 1: Check if NIP is in filename
        if (strpos($filename, $nip) !== false) {
            return true;
        }

        // Method 2: Check document content (more accurate but slower)
        // We'll read the document XML to find NIP
        try {
            $zip = new \ZipArchive();
            if ($zip->open($filePath) === TRUE) {
                $content = $zip->getFromName('word/document.xml');
                $zip->close();

                if ($content && strpos($content, $nip) !== false) {
                    return true;
                }
            }
        } catch (\Exception $e) {
            // If we can't read the document, fall back to filename check
            Log::warning('DocumentController: Could not read document content', [
                'file' => $filePath,
                'error' => $e->getMessage()
            ]);
        }

        return false;
    }

    /**
     * Extract template name from generated filename
     */
    private function extractTemplateNameFromFilename($filename)
    {
        // Remove timestamp suffix and .docx extension
        // Example: "TEMPLATE_NAME_2025-07-19_11-36-52.docx" -> "TEMPLATE_NAME"
        $name = preg_replace('/_\d{4}-\d{2}-\d{2}_\d{2}-\d{2}-\d{2}\.docx$/', '', $filename);
        return $name;
    }

    /**
     * Save generated document record to database
     */
    private function saveGeneratedDocumentRecord($pejabatData, $pegawaiData, $templateFilename, $filePath, $generatedFilename, $templateData, $request)
    {
        try {
            // Extract template name from filename
            $templateName = $this->extractTemplateNameFromFilename($templateFilename);

            // Get file size
            $fileSize = file_exists($filePath) ? filesize($filePath) : 0;

            // Get client IP
            $generatedBy = $request->ip();

            GeneratedDocument::create([
                'pegawai_nip' => $pegawaiData['nip'] ?? '',
                'pegawai_nama' => $pegawaiData['nama'] ?? '',
                'pegawai_golongan' => $pegawaiData['golongan'] ?? '',
                'pegawai_jabatan' => $pegawaiData['jabatan'] ?? '',
                'pegawai_unit_kerja' => $pegawaiData['unit_kerja'] ?? '',

                'pejabatttd' => $pejabatData['nama'] ?? '',
                'nippejabatttd' => $pejabatData['nip'] ?? '',

                'template_name' => $templateName,
                'template_filename' => $templateFilename,
                'generated_filename' => $generatedFilename,
                'file_path' => $filePath,
                'file_size' => $fileSize,

                'generated_by' => $generatedBy,
                'generated_at' => now(),
                'template_variables' => $templateData
            ]);

            Log::info('DocumentController: Document record saved to database', [
                'pegawai_nip' => $pegawaiData['nip'] ?? '',
                'template_name' => $templateName,
                'generated_filename' => $generatedFilename
            ]);

        } catch (\Exception $e) {
            Log::error('DocumentController: Failed to save document record', [
                'error' => $e->getMessage(),
                'pegawai_nip' => $pegawaiData['nip'] ?? '',
                'template_filename' => $templateFilename
            ]);
        }
    }

    /**
     * Extract jabatan sebelum kata "pada"
     * Contoh: "Pranata Komputer Ahli Pertama pada Bagian Tata Usaha" -> "Pranata Komputer Ahli Pertama"
     */
    private function extractJabatanSebelumPada($jabatan)
    {
        if (empty($jabatan)) {
            return '';
        }

        // Cari posisi kata "pada" (case insensitive)
        $posisiPada = stripos($jabatan, ' pada ');

        if ($posisiPada !== false) {
            // Ambil bagian sebelum " pada "
            return trim(substr($jabatan, 0, $posisiPada));
        }

        // Jika tidak ada kata "pada", return jabatan asli
        return $jabatan;
    }

    /**
     * Format tanggal ke bahasa Indonesia
     */
    private function formatTanggalIndonesia($tanggal)
    {
        $bulan = [
            1 => 'Januari', 2 => 'Februari', 3 => 'Maret', 4 => 'April',
            5 => 'Mei', 6 => 'Juni', 7 => 'Juli', 8 => 'Agustus',
            9 => 'September', 10 => 'Oktober', 11 => 'November', 12 => 'Desember'
        ];

        $timestamp = strtotime($tanggal);
        $hari = date('d', $timestamp);
        $bulanNama = $bulan[date('n', $timestamp)];
        $tahun = date('Y', $timestamp);

        return $hari . ' ' . $bulanNama . ' ' . $tahun;
    }

    /**
     * Get nama hari dalam bahasa Indonesia
     */
    private function getHariIndonesia($dayOfWeek)
    {
        $hari = [
            0 => 'Minggu', 1 => 'Senin', 2 => 'Selasa', 3 => 'Rabu',
            4 => 'Kamis', 5 => 'Jumat', 6 => 'Sabtu'
        ];

        return $hari[$dayOfWeek] ?? '';
    }

    /**
     * Get nama bulan dalam bahasa Indonesia
     */
    private function getBulanIndonesia($month)
    {
        $bulan = [
            1 => 'Januari', 2 => 'Februari', 3 => 'Maret', 4 => 'April',
            5 => 'Mei', 6 => 'Juni', 7 => 'Juli', 8 => 'Agustus',
            9 => 'September', 10 => 'Oktober', 11 => 'November', 12 => 'Desember'
        ];

        return $bulan[$month] ?? '';
    }

    /**
     * Download generated document
     */
    public function downloadDocument($filename)
    {
        $filePath = storage_path('app/generated/' . $filename);

        if (!file_exists($filePath)) {
            abort(404, 'File tidak ditemukan');
        }

        return Response::download($filePath, $filename);
    }
}
