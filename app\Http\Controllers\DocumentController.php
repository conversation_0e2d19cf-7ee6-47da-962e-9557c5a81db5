<?php

namespace App\Http\Controllers;

use App\Services\DocumentGeneratorService;
use App\Services\PegawaiApiService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Response;

class DocumentController extends Controller
{
    protected $documentGenerator;
    protected $pegawaiApiService;

    public function __construct(DocumentGeneratorService $documentGenerator, PegawaiApiService $pegawaiApiService)
    {
        $this->documentGenerator = $documentGenerator;
        $this->pegawaiApiService = $pegawaiApiService;
    }

    /**
     * Show document generation form
     */
    public function index()
    {
        $templates = $this->documentGenerator->getAvailableTemplates();
        
        return view('documents.index', compact('templates'));
    }

    /**
     * Generate document
     */
    public function generate(Request $request)
    {
        $request->validate([
            'template' => 'required|string',
            'pejabat_nip' => 'required|string',
            'pegawai_nip' => 'required|string',
        ]);

        try {
            // Get pejabat data from API
            $pejabatResult = $this->pegawaiApiService->getPegawaiByNip($request->pejabat_nip);

            if (!$pejabatResult['success']) {
                return response()->json([
                    'success' => false,
                    'message' => 'Gagal mengambil data pejabat: ' . ($pejabatResult['message'] ?? 'Unknown error')
                ]);
            }

            // Get pegawai data from API
            $pegawaiResult = $this->pegawaiApiService->getPegawaiByNip($request->pegawai_nip);

            if (!$pegawaiResult['success']) {
                return response()->json([
                    'success' => false,
                    'message' => 'Gagal mengambil data pegawai: ' . ($pegawaiResult['message'] ?? 'Unknown error')
                ]);
            }

            $pejabatData = $pejabatResult['data'];
            $pegawaiData = $pegawaiResult['data'];

            Log::info('DocumentController: Data retrieved', [
                'pejabat_nip' => $request->pejabat_nip,
                'pejabat_nama' => $pejabatData['nama'] ?? 'N/A',
                'pegawai_nip' => $request->pegawai_nip,
                'pegawai_nama' => $pegawaiData['nama'] ?? 'N/A'
            ]);

            // Prepare data for template replacement
            $templateData = $this->prepareTemplateData($pejabatData, $pegawaiData, $request->all());

            // Generate document
            $result = $this->documentGenerator->generateDocument(
                $request->template,
                $templateData
            );

            if ($result['success']) {
                return response()->json([
                    'success' => true,
                    'message' => 'Dokumen berhasil dibuat',
                    'download_url' => $result['download_url'],
                    'file_name' => $result['file_name']
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => $result['message']
                ]);
            }

        } catch (\Exception $e) {
            Log::error('DocumentController: Generation failed', [
                'error' => $e->getMessage(),
                'template' => $request->template,
                'pejabat_nip' => $request->pejabat_nip ?? 'N/A',
                'pegawai_nip' => $request->pegawai_nip ?? 'N/A'
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat membuat dokumen: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Download generated document
     */
    public function download($filename)
    {
        $filePath = storage_path('app/generated/' . $filename);
        
        if (!file_exists($filePath)) {
            abort(404, 'File not found');
        }

        return Response::download($filePath, $filename, [
            'Content-Type' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        ]);
    }

    /**
     * Get template variables
     */
    public function getTemplateVariables(Request $request)
    {
        $request->validate([
            'template' => 'required|string'
        ]);

        $variables = $this->documentGenerator->getTemplateVariables($request->template);
        
        return response()->json([
            'success' => true,
            'variables' => $variables
        ]);
    }

    /**
     * Prepare data for template replacement
     */
    private function prepareTemplateData($pejabatData, $pegawaiData, $requestData)
    {
        // Prepare data with separate pejabat and pegawai information
        $data = [
            // Standard format (uppercase) - using pegawai data as default
            'NAMA' => $pegawaiData['nama'] ?? '',
            'NIP' => $pegawaiData['nip'] ?? '',
            'GOLONGAN' => $pegawaiData['golongan'] ?? '',
            'JABATAN' => $pegawaiData['jabatan'] ?? '',
            'UNIT_KERJA' => $pegawaiData['unit_kerja'] ?? '',
            'INDUK_UNIT' => $pegawaiData['induk_unit'] ?? '',
            'TMT_PENSIUN' => $pegawaiData['tmt_pensiun'] ?? '',

            // Pegawai data (yang dimaksud dalam surat)
            'namapegawai' => $pegawaiData['nama'] ?? '',
            'nippegawai' => $pegawaiData['nip'] ?? '',
            'pangkatgolpegawai' => $pegawaiData['golongan'] ?? '',
            'jabatanpegawai' => $pegawaiData['jabatan'] ?? '',
            'tempattugas' => $pegawaiData['unitkerja'] ?? $pegawaiData['unit_kerja'] ?? '',

            // Pejabat data (yang menandatangani surat)
            'namapejabat' => $pejabatData['nama'] ?? '',
            'nippejabat' => $pejabatData['nip'] ?? '',
            'pangkatgolpejabat' => $pejabatData['golongan'] ?? '',
            'jabatanpejabat' => $pejabatData['jabatan'] ?? '',
            'ukerpejabat' => $pejabatData['unit_kerja'] ?? '',
        ];

        // Add current date and time
        $data['TANGGAL'] = date('d F Y');
        $data['TANGGAL_INDO'] = $this->formatTanggalIndonesia(date('Y-m-d'));
        $data['HARI'] = $this->getHariIndonesia(date('w'));
        $data['BULAN'] = $this->getBulanIndonesia(date('n'));
        $data['TAHUN'] = date('Y');

        // Template specific date formats
        $data['dd-mm-yyyy'] = date('d-m-Y');

        // Office/location data (from form input or default)
        $data['kabkota'] = $requestData['kabkota'] ?? 'Mataram';
        $data['jln'] = $requestData['jln'] ?? 'Jl. Pendidikan No. 35';
        $data['telfon'] = $requestData['telfon'] ?? '(0370) 621974';
        $data['fax'] = $requestData['fax'] ?? '(0370) 621974';
        $data['email'] = $requestData['email'] ?? '<EMAIL>';

        // Document numbering (from form input or auto-generated)
        $data['nosrt'] = $requestData['nosrt'] ?? 'B-' . rand(1000, 9999);
        $data['blnno'] = $requestData['blnno'] ?? date('m');
        $data['thnno'] = $requestData['thnno'] ?? date('Y');

        // Add any additional data from request
        foreach ($requestData as $key => $value) {
            if (!in_array($key, ['template', 'pejabat_nip', 'pegawai_nip', '_token']) && is_string($value)) {
                $data[strtoupper($key)] = $value;
                $data[strtolower($key)] = $value;
            }
        }

        Log::info('DocumentController: Template data prepared', [
            'data_keys' => array_keys($data)
        ]);

        return $data;
    }

    /**
     * Format tanggal ke bahasa Indonesia
     */
    private function formatTanggalIndonesia($tanggal)
    {
        $bulan = [
            1 => 'Januari', 2 => 'Februari', 3 => 'Maret', 4 => 'April',
            5 => 'Mei', 6 => 'Juni', 7 => 'Juli', 8 => 'Agustus',
            9 => 'September', 10 => 'Oktober', 11 => 'November', 12 => 'Desember'
        ];

        $timestamp = strtotime($tanggal);
        $hari = date('d', $timestamp);
        $bulanNama = $bulan[date('n', $timestamp)];
        $tahun = date('Y', $timestamp);

        return $hari . ' ' . $bulanNama . ' ' . $tahun;
    }

    /**
     * Get nama hari dalam bahasa Indonesia
     */
    private function getHariIndonesia($dayOfWeek)
    {
        $hari = [
            0 => 'Minggu', 1 => 'Senin', 2 => 'Selasa', 3 => 'Rabu',
            4 => 'Kamis', 5 => 'Jumat', 6 => 'Sabtu'
        ];

        return $hari[$dayOfWeek] ?? '';
    }

    /**
     * Get nama bulan dalam bahasa Indonesia
     */
    private function getBulanIndonesia($month)
    {
        $bulan = [
            1 => 'Januari', 2 => 'Februari', 3 => 'Maret', 4 => 'April',
            5 => 'Mei', 6 => 'Juni', 7 => 'Juli', 8 => 'Agustus',
            9 => 'September', 10 => 'Oktober', 11 => 'November', 12 => 'Desember'
        ];

        return $bulan[$month] ?? '';
    }
}
