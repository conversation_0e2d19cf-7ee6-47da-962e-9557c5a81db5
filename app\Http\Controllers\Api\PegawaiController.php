<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\PegawaiApiService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class PegawaiController extends Controller
{
    private $pegawaiApiService;

    public function __construct(PegawaiApiService $pegawaiApiService)
    {
        $this->pegawaiApiService = $pegawaiApiService;
    }

    /**
     * Get all pegawai with pagination and search
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $page = $request->get('page', 1);
            $limit = $request->get('limit', 20);
            $search = $request->get('search');

            $result = $this->pegawaiApiService->getAllPegawai($page, $limit, $search);
            
            if ($result['success']) {
                return response()->json([
                    'success' => true,
                    'data' => $result['data']
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => $result['message'] ?? 'Failed to get pegawai data'
            ], 400);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get pegawai data: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Search pegawai by name or NIP
     */
    public function search(Request $request): JsonResponse
    {
        try {
            $query = $request->get('q', $request->get('search', ''));
            $limit = $request->get('limit', 10);

            if (empty($query)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Search query is required'
                ], 400);
            }

            $result = $this->pegawaiApiService->searchPegawai($query, $limit);
            
            if ($result['success']) {
                return response()->json([
                    'success' => true,
                    'data' => $result['data']
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => $result['message'] ?? 'Search failed'
            ], 400);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Search failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get pegawai by NIP
     */
    public function show(string $nip): JsonResponse
    {
        try {
            $result = $this->pegawaiApiService->getPegawaiByNip($nip);
            
            if ($result['success']) {
                return response()->json([
                    'success' => true,
                    'data' => $result['data']
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => $result['message'] ?? 'Pegawai not found'
            ], 404);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get pegawai: ' . $e->getMessage()
            ], 500);
        }
    }
}
