<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use App\Services\PegawaiApiService;

class PegawaiApiServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->app->singleton(PegawaiApiService::class, function ($app) {
            return new PegawaiApiService();
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        //
    }
}
