<?php

namespace App\Livewire;

use Livewire\Component;
use App\Services\PegawaiApiService;
use Livewire\Attributes\On;

class PegawaiSearch extends Component
{
    public $search = '';
    public $results = [];
    public $selectedPegawai = null;
    public $isLoading = false;
    public $showResults = false;
    public $minSearchLength = 3;
    public $placeholder = 'Cari pegawai berdasarkan NIP atau nama...';
    public $label = 'Cari Pegawai';
    public $componentId = 'pegawai-search';

    // Selected pegawai data
    public $nip = '';
    public $nama = '';
    public $golongan = '';
    public $jabatan = '';
    public $unit_kerja = '';
    public $induk_unit = '';
    public $tmt_pensiun = '';

    protected $pegawaiApiService;

    public function mount($placeholder = null, $label = null, $componentId = null)
    {
        if ($placeholder) {
            $this->placeholder = $placeholder;
        }
        if ($label) {
            $this->label = $label;
        }
        if ($componentId) {
            $this->componentId = $componentId;
        }
    }

    public function updatedSearch()
    {
        if (strlen($this->search) >= $this->minSearchLength) {
            $this->searchPegawai();
        } else {
            $this->results = [];
            $this->showResults = false;
        }
    }

    public function searchPegawai()
    {
        $this->isLoading = true;
        $this->showResults = true;

        try {
            // Get service instance
            if (!$this->pegawaiApiService) {
                $this->pegawaiApiService = app(PegawaiApiService::class);
            }

            \Log::info('Livewire: Searching pegawai', ['query' => $this->search]);

            // Direct call to external API
            $result = $this->pegawaiApiService->searchPegawai($this->search, 10);

            \Log::info('Livewire: Search result', ['result' => $result]);

            if ($result && isset($result['success']) && $result['success']) {
                $this->results = $result['data']['data'] ?? $result['data'] ?? [];

                \Log::info('Livewire: Results found', ['count' => count($this->results)]);

                if (empty($this->results)) {
                    $this->dispatch('show-toast', [
                        'message' => 'Tidak ada pegawai yang ditemukan',
                        'type' => 'info'
                    ]);
                }
            } else {
                $this->results = [];
                $errorMessage = $result['message'] ?? 'Gagal mencari pegawai';

                \Log::error('Livewire: Search failed', ['error' => $errorMessage, 'result' => $result]);

                $this->dispatch('show-toast', [
                    'message' => $errorMessage,
                    'type' => 'error'
                ]);
            }
        } catch (\Exception $e) {
            $this->results = [];

            \Log::error('Livewire: Search exception', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            $this->dispatch('show-toast', [
                'message' => 'Terjadi kesalahan: ' . $e->getMessage(),
                'type' => 'error'
            ]);
        } finally {
            $this->isLoading = false;
        }
    }

    public function selectPegawai($index)
    {
        if (!isset($this->results[$index])) {
            return;
        }

        $pegawai = $this->results[$index];
        $this->selectedPegawai = $pegawai;
        $this->nip = $pegawai['nip'] ?? '';
        $this->nama = $pegawai['nama'] ?? '';
        $this->golongan = $pegawai['golongan'] ?? '';
        $this->jabatan = $pegawai['jabatan'] ?? '';
        $this->unit_kerja = $pegawai['unit_kerja'] ?? '';
        $this->induk_unit = $pegawai['induk_unit'] ?? '';
        $this->tmt_pensiun = $pegawai['tmt_pensiun'] ?? '';

        $this->search = $this->nama . ' (' . $this->nip . ')';
        $this->showResults = false;
        $this->results = [];

        $this->dispatch('pegawai-selected', [
            'componentId' => $this->componentId,
            'pegawai' => $pegawai
        ]);

        $this->dispatch('show-toast', [
            'message' => 'Pegawai berhasil dipilih: ' . $this->nama,
            'type' => 'success'
        ]);
    }

    public function clearSelection()
    {
        $this->selectedPegawai = null;
        $this->search = '';
        $this->results = [];
        $this->showResults = false;

        // Clear all pegawai data
        $this->nip = '';
        $this->nama = '';
        $this->golongan = '';
        $this->jabatan = '';
        $this->unit_kerja = '';
        $this->induk_unit = '';
        $this->tmt_pensiun = '';

        $this->dispatch('pegawai-cleared', [
            'componentId' => $this->componentId
        ]);
    }

    public function hideResults()
    {
        // Delay hiding results to allow click events to fire
        $this->dispatch('hide-results-delayed');
    }

    #[On('hide-results-delayed')]
    public function hideResultsDelayed()
    {
        $this->showResults = false;
    }

    #[On('clear-all-selections')]
    public function clearAllSelections()
    {
        $this->clearSelection();
    }

    public function render()
    {
        return view('livewire.pegawai-search');
    }
}
