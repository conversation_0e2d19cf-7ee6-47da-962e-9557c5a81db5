<div class="relative w-full" id="<?php echo e($componentId); ?>">
    <!-- Label -->
    <label for="search-<?php echo e($componentId); ?>" class="block text-sm font-medium text-gray-700 mb-2">
        <?php echo e($label); ?>

    </label>

    <!-- Search Input -->
    <div class="relative">
        <input
            type="text"
            id="search-<?php echo e($componentId); ?>"
            wire:model.live.debounce.300ms="search"
            wire:focus="$set('showResults', true)"
            placeholder="<?php echo e($placeholder); ?>"
            class="w-full px-4 py-3 pr-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 <?php $__errorArgs = ['search'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
            autocomplete="off"
        />

        <!-- Loading Spinner -->
        <div wire:loading wire:target="searchPegawai" class="absolute right-3 top-1/2 transform -translate-y-1/2">
            <svg class="animate-spin h-5 w-5 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
        </div>

        <!-- Clear Button -->
        <!--[if BLOCK]><![endif]--><?php if($selectedPegawai): ?>
            <button
                type="button"
                wire:click="clearSelection"
                class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors duration-200"
                title="Hapus pilihan"
            >
                <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
    </div>

    <!-- Search Results Dropdown -->
    <!--[if BLOCK]><![endif]--><?php if($showResults && !empty($results)): ?>
        <div class="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto">
            <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $results; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $pegawai): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div
                    wire:click="selectPegawai(<?php echo e($index); ?>)"
                    class="px-4 py-3 hover:bg-blue-50 cursor-pointer border-b border-gray-100 last:border-b-0 transition-colors duration-150"
                >
                    <div class="flex justify-between items-start">
                        <div class="flex-1">
                            <div class="font-medium text-gray-900"><?php echo e($pegawai['nama'] ?? 'N/A'); ?></div>
                            <div class="text-sm text-gray-600">NIP: <?php echo e($pegawai['nip'] ?? 'N/A'); ?></div>
                            <!--[if BLOCK]><![endif]--><?php if(isset($pegawai['jabatan'])): ?>
                                <div class="text-sm text-gray-500"><?php echo e($pegawai['jabatan']); ?></div>
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                        </div>
                        <div class="text-right text-sm text-gray-500">
                            <!--[if BLOCK]><![endif]--><?php if(isset($pegawai['golongan'])): ?>
                                <div><?php echo e($pegawai['golongan']); ?></div>
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                            <!--[if BLOCK]><![endif]--><?php if(isset($pegawai['unit_kerja'])): ?>
                                <div class="truncate max-w-32" title="<?php echo e($pegawai['unit_kerja']); ?>">
                                    <?php echo e(Str::limit($pegawai['unit_kerja'], 20)); ?>

                                </div>
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                        </div>
                    </div>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
        </div>
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

    <!-- No Results Message -->
    <!--[if BLOCK]><![endif]--><?php if($showResults && empty($results) && strlen($search) >= $minSearchLength && !$isLoading): ?>
        <div class="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg">
            <div class="px-4 py-3 text-gray-500 text-center">
                <svg class="mx-auto h-8 w-8 text-gray-400 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                </svg>
                Tidak ada pegawai yang ditemukan
            </div>
        </div>
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

    <!-- Selected Pegawai Info -->
    <!--[if BLOCK]><![endif]--><?php if($selectedPegawai): ?>
        <div class="mt-4 p-4 bg-green-50 border border-green-200 rounded-lg">
            <div class="flex justify-between items-start">
                <div class="flex-1">
                    <h4 class="font-medium text-green-900 mb-2">Pegawai Terpilih</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                        <div>
                            <span class="font-medium text-gray-700">Nama:</span>
                            <span class="text-gray-900 ml-2"><?php echo e($nama); ?></span>
                        </div>
                        <div>
                            <span class="font-medium text-gray-700">NIP:</span>
                            <span class="text-gray-900 ml-2"><?php echo e($nip); ?></span>
                        </div>
                        <!--[if BLOCK]><![endif]--><?php if($golongan): ?>
                            <div>
                                <span class="font-medium text-gray-700">Golongan:</span>
                                <span class="text-gray-900 ml-2"><?php echo e($golongan); ?></span>
                            </div>
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                        <!--[if BLOCK]><![endif]--><?php if($jabatan): ?>
                            <div>
                                <span class="font-medium text-gray-700">Jabatan:</span>
                                <span class="text-gray-900 ml-2"><?php echo e($jabatan); ?></span>
                            </div>
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                        <!--[if BLOCK]><![endif]--><?php if($unit_kerja): ?>
                            <div class="md:col-span-2">
                                <span class="font-medium text-gray-700">Unit Kerja:</span>
                                <span class="text-gray-900 ml-2"><?php echo e($unit_kerja); ?></span>
                            </div>
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                        <!--[if BLOCK]><![endif]--><?php if($induk_unit): ?>
                            <div class="md:col-span-2">
                                <span class="font-medium text-gray-700">Induk Unit:</span>
                                <span class="text-gray-900 ml-2"><?php echo e($induk_unit); ?></span>
                            </div>
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                        <!--[if BLOCK]><![endif]--><?php if($tmt_pensiun): ?>
                            <div>
                                <span class="font-medium text-gray-700">TMT Pensiun:</span>
                                <span class="text-gray-900 ml-2"><?php echo e($tmt_pensiun); ?></span>
                            </div>
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                    </div>
                </div>
                <button
                    type="button"
                    wire:click="clearSelection"
                    class="ml-4 text-green-600 hover:text-green-800 transition-colors duration-200"
                    title="Hapus pilihan"
                >
                    <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
        </div>
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

    <!-- Error Message -->
    <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['search'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
        <p class="mt-2 text-sm text-red-600"><?php echo e($message); ?></p>
    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->

    <!-- Help Text -->
    <p class="mt-2 text-sm text-gray-500">
        Ketik minimal <?php echo e($minSearchLength); ?> karakter untuk mencari pegawai berdasarkan NIP atau nama
    </p>
</div>

<script>
    document.addEventListener('livewire:init', () => {
        Livewire.on('hide-results-delayed', () => {
            setTimeout(() => {
                Livewire.dispatch('hide-results-delayed');
            }, 200);
        });
    });

    // Handle click outside to close dropdown
    document.addEventListener('click', function(event) {
        const searchComponents = document.querySelectorAll('[id^="<?php echo e($componentId); ?>"]');
        searchComponents.forEach(component => {
            if (!component.contains(event.target)) {
                // Find the Livewire component and hide results
                const livewireComponent = component.closest('[wire\\:id]');
                if (livewireComponent) {
                    Livewire.find(livewireComponent.getAttribute('wire:id')).set('showResults', false);
                }
            }
        });
    });
</script>
<?php /**PATH C:\Users\<USER>\Documents\code\mutasites\resources\views/livewire/pegawai-search.blade.php ENDPATH**/ ?>