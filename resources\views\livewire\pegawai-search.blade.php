<div class="relative w-full" id="{{ $componentId }}">
    <!-- Label -->
    <label for="search-{{ $componentId }}" class="block text-sm font-medium text-gray-700 mb-2">
        {{ $label }}
    </label>

    <!-- Search Input -->
    <div class="relative">
        <input
            type="text"
            id="search-{{ $componentId }}"
            wire:model.live.debounce.300ms="search"
            wire:focus="$set('showResults', true)"
            wire:blur="hideResults"
            placeholder="{{ $placeholder }}"
            class="w-full px-4 py-3 pr-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 @error('search') border-red-500 @enderror"
            autocomplete="off"
        />

        <!-- Loading Spinner -->
        <div wire:loading wire:target="searchPegawai" class="absolute right-3 top-1/2 transform -translate-y-1/2">
            <svg class="animate-spin h-5 w-5 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
        </div>

        <!-- Clear Button -->
        @if($selectedPegawai)
            <button
                type="button"
                wire:click="clearSelection"
                class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors duration-200"
                title="Hapus pilihan"
            >
                <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        @endif
    </div>

    <!-- Search Results Dropdown -->
    @if($showResults && !empty($results))
        <div class="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto">
            @foreach($results as $pegawai)
                <div
                    wire:click="selectPegawai({{ json_encode($pegawai) }})"
                    class="px-4 py-3 hover:bg-blue-50 cursor-pointer border-b border-gray-100 last:border-b-0 transition-colors duration-150"
                >
                    <div class="flex justify-between items-start">
                        <div class="flex-1">
                            <div class="font-medium text-gray-900">{{ $pegawai['nama'] ?? 'N/A' }}</div>
                            <div class="text-sm text-gray-600">NIP: {{ $pegawai['nip'] ?? 'N/A' }}</div>
                            @if(isset($pegawai['jabatan']))
                                <div class="text-sm text-gray-500">{{ $pegawai['jabatan'] }}</div>
                            @endif
                        </div>
                        <div class="text-right text-sm text-gray-500">
                            @if(isset($pegawai['golongan']))
                                <div>{{ $pegawai['golongan'] }}</div>
                            @endif
                            @if(isset($pegawai['unit_kerja']))
                                <div class="truncate max-w-32" title="{{ $pegawai['unit_kerja'] }}">
                                    {{ Str::limit($pegawai['unit_kerja'], 20) }}
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            @endforeach
        </div>
    @endif

    <!-- No Results Message -->
    @if($showResults && empty($results) && strlen($search) >= $minSearchLength && !$isLoading)
        <div class="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg">
            <div class="px-4 py-3 text-gray-500 text-center">
                <svg class="mx-auto h-8 w-8 text-gray-400 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                </svg>
                Tidak ada pegawai yang ditemukan
            </div>
        </div>
    @endif

    <!-- Selected Pegawai Info -->
    @if($selectedPegawai)
        <div class="mt-4 p-4 bg-green-50 border border-green-200 rounded-lg">
            <div class="flex justify-between items-start">
                <div class="flex-1">
                    <h4 class="font-medium text-green-900 mb-2">Pegawai Terpilih</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                        <div>
                            <span class="font-medium text-gray-700">Nama:</span>
                            <span class="text-gray-900 ml-2">{{ $nama }}</span>
                        </div>
                        <div>
                            <span class="font-medium text-gray-700">NIP:</span>
                            <span class="text-gray-900 ml-2">{{ $nip }}</span>
                        </div>
                        @if($golongan)
                            <div>
                                <span class="font-medium text-gray-700">Golongan:</span>
                                <span class="text-gray-900 ml-2">{{ $golongan }}</span>
                            </div>
                        @endif
                        @if($jabatan)
                            <div>
                                <span class="font-medium text-gray-700">Jabatan:</span>
                                <span class="text-gray-900 ml-2">{{ $jabatan }}</span>
                            </div>
                        @endif
                        @if($unit_kerja)
                            <div class="md:col-span-2">
                                <span class="font-medium text-gray-700">Unit Kerja:</span>
                                <span class="text-gray-900 ml-2">{{ $unit_kerja }}</span>
                            </div>
                        @endif
                        @if($induk_unit)
                            <div class="md:col-span-2">
                                <span class="font-medium text-gray-700">Induk Unit:</span>
                                <span class="text-gray-900 ml-2">{{ $induk_unit }}</span>
                            </div>
                        @endif
                        @if($tmt_pensiun)
                            <div>
                                <span class="font-medium text-gray-700">TMT Pensiun:</span>
                                <span class="text-gray-900 ml-2">{{ $tmt_pensiun }}</span>
                            </div>
                        @endif
                    </div>
                </div>
                <button
                    type="button"
                    wire:click="clearSelection"
                    class="ml-4 text-green-600 hover:text-green-800 transition-colors duration-200"
                    title="Hapus pilihan"
                >
                    <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
        </div>
    @endif

    <!-- Error Message -->
    @error('search')
        <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
    @enderror

    <!-- Help Text -->
    <p class="mt-2 text-sm text-gray-500">
        Ketik minimal {{ $minSearchLength }} karakter untuk mencari pegawai berdasarkan NIP atau nama
    </p>
</div>

<script>
    document.addEventListener('livewire:init', () => {
        Livewire.on('hide-results-delayed', () => {
            setTimeout(() => {
                Livewire.dispatch('hide-results-delayed');
            }, 200);
        });
    });
</script>
