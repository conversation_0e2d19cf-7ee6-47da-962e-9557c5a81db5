<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\GeneratedDocument;
use Illuminate\Support\Facades\DB;

class DocumentStats extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'documents:stats {--detailed : Show detailed statistics}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Show document generation statistics';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('📊 Document Generation Statistics');
        $this->line('');

        // Basic stats
        $totalDocuments = GeneratedDocument::count();
        $uniquePegawai = GeneratedDocument::distinct('pegawai_nip')->count();
        $totalFileSize = GeneratedDocument::sum('file_size');

        $this->table(['Metric', 'Value'], [
            ['Total Documents Generated', number_format($totalDocuments)],
            ['Unique Pegawai', number_format($uniquePegawai)],
            ['Total File Size', $this->formatBytes($totalFileSize)],
            ['Average per Pegawai', $uniquePegawai > 0 ? number_format($totalDocuments / $uniquePegawai, 2) : '0']
        ]);

        // Template usage stats
        $this->line('');
        $this->info('📋 Template Usage:');
        $templateStats = GeneratedDocument::select('template_name')
            ->selectRaw('COUNT(*) as count')
            ->groupBy('template_name')
            ->orderBy('count', 'desc')
            ->get();

        $templateData = [];
        foreach ($templateStats as $stat) {
            $templateData[] = [$stat->template_name, $stat->count];
        }

        if (!empty($templateData)) {
            $this->table(['Template Name', 'Usage Count'], $templateData);
        } else {
            $this->warn('No template usage data found.');
        }

        // Recent activity
        $this->line('');
        $this->info('🕒 Recent Activity (Last 7 days):');
        $recentDocs = GeneratedDocument::where('generated_at', '>=', now()->subDays(7))
            ->orderBy('generated_at', 'desc')
            ->limit(10)
            ->get();

        if ($recentDocs->count() > 0) {
            $recentData = [];
            foreach ($recentDocs as $doc) {
                $recentData[] = [
                    $doc->generated_at->format('Y-m-d H:i'),
                    $doc->pegawai_nama,
                    $doc->pegawai_nip,
                    $doc->template_name,
                    $doc->pejabatttd ?? 'N/A'
                ];
            }
            $this->table(['Date', 'Pegawai', 'NIP', 'Template', 'Pejabat TTD'], $recentData);
        } else {
            $this->warn('No recent activity found.');
        }

        // Detailed stats if requested
        if ($this->option('detailed')) {
            $this->showDetailedStats();
        }
    }

    private function showDetailedStats()
    {
        $this->line('');
        $this->info('📈 Detailed Statistics:');

        // Top 10 most active pegawai
        $topPegawai = GeneratedDocument::select('pegawai_nip', 'pegawai_nama')
            ->selectRaw('COUNT(*) as document_count')
            ->groupBy('pegawai_nip', 'pegawai_nama')
            ->orderBy('document_count', 'desc')
            ->limit(10)
            ->get();

        if ($topPegawai->count() > 0) {
            $this->line('');
            $this->info('👥 Top 10 Most Active Pegawai:');
            $topData = [];
            foreach ($topPegawai as $pegawai) {
                $topData[] = [
                    $pegawai->pegawai_nama,
                    $pegawai->pegawai_nip,
                    $pegawai->document_count
                ];
            }
            $this->table(['Name', 'NIP', 'Documents'], $topData);
        }

        // Monthly generation stats
        $monthlyStats = GeneratedDocument::selectRaw('DATE_FORMAT(generated_at, "%Y-%m") as month')
            ->selectRaw('COUNT(*) as count')
            ->groupBy('month')
            ->orderBy('month', 'desc')
            ->limit(12)
            ->get();

        if ($monthlyStats->count() > 0) {
            $this->line('');
            $this->info('📅 Monthly Generation Stats:');
            $monthlyData = [];
            foreach ($monthlyStats as $stat) {
                $monthlyData[] = [$stat->month, $stat->count];
            }
            $this->table(['Month', 'Documents Generated'], $monthlyData);
        }
    }

    private function formatBytes($bytes, $precision = 2)
    {
        $units = array('B', 'KB', 'MB', 'GB', 'TB');

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, $precision) . ' ' . $units[$i];
    }
}
