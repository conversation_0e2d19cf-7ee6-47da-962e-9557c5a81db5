<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\GeneratedDocument;

class CheckDocumentData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'documents:check-data';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check document data in database';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $documents = GeneratedDocument::all();

        $this->info('Documents in database:');

        foreach ($documents as $doc) {
            $this->line("ID: {$doc->id}");
            $this->line("Pegawai NIP: {$doc->pegawai_nip}");
            $this->line("Pegawai Nama: {$doc->pegawai_nama}");
            $this->line("Template: {$doc->template_name}");
            $this->line("Generated: {$doc->generated_at}");
            $this->line("---");
        }

        $this->info("Total documents: " . $documents->count());
    }
}
