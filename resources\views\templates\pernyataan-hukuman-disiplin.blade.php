@extends('layouts.app')

@section('title', $templateName)

@section('content')
<div class="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">
    <!-- Header Section -->
    <div class="bg-white shadow-sm border-b border-gray-100">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">{{ $templateName }}</h1>
                    <p class="mt-1 text-sm text-gray-600">Generate surat pernyataan tidak sedang menjalani proses hukuman disiplin</p>
                </div>
                <a href="{{ route('documents.list') }}" 
                   class="inline-flex items-center px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 text-sm font-medium rounded-lg transition-colors duration-200">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                    Lihat Dokumen
                </a>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Form Card -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
            <div class="px-6 py-4 bg-gradient-to-r from-blue-600 to-indigo-600">
                <h2 class="text-lg font-semibold text-white">Form Generate Dokumen</h2>
                <p class="text-blue-100 text-sm mt-1">Isi data pegawai dan pejabat penandatangan</p>
            </div>

            <form id="generateForm" class="p-6 space-y-6">
                @csrf
                
                <!-- Pegawai Section -->
                <div class="space-y-4">
                    <div class="flex items-center space-x-2">
                        <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                            <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                            </svg>
                        </div>
                        <h3 class="text-lg font-medium text-gray-900">Data Pegawai</h3>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="nip_pegawai" class="block text-sm font-medium text-gray-700 mb-2">
                                NIP Pegawai <span class="text-red-500">*</span>
                            </label>
                            <input type="text" 
                                   id="nip_pegawai" 
                                   name="nip_pegawai" 
                                   maxlength="18"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200"
                                   placeholder="Masukkan 18 digit NIP pegawai"
                                   required>
                            <p class="mt-1 text-xs text-gray-500">18 digit NIP pegawai yang akan dibuatkan surat</p>
                        </div>

                        <div>
                            <label for="keperluan" class="block text-sm font-medium text-gray-700 mb-2">
                                Keperluan
                            </label>
                            <input type="text" 
                                   id="keperluan" 
                                   name="keperluan" 
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200"
                                   placeholder="Keperluan surat (opsional)">
                            <p class="mt-1 text-xs text-gray-500">Kosongkan jika untuk keperluan administrasi umum</p>
                        </div>
                    </div>
                </div>

                <!-- Pejabat Section -->
                <div class="space-y-4 pt-6 border-t border-gray-200">
                    <div class="flex items-center space-x-2">
                        <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                            <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <h3 class="text-lg font-medium text-gray-900">Pejabat Penandatangan</h3>
                    </div>

                    <div>
                        <label for="nip_pejabat" class="block text-sm font-medium text-gray-700 mb-2">
                            NIP Pejabat <span class="text-red-500">*</span>
                        </label>
                        <input type="text" 
                               id="nip_pejabat" 
                               name="nip_pejabat" 
                               maxlength="18"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200"
                               placeholder="Masukkan 18 digit NIP pejabat penandatangan"
                               required>
                        <p class="mt-1 text-xs text-gray-500">18 digit NIP pejabat yang akan menandatangani surat</p>
                    </div>
                </div>

                <!-- Submit Button -->
                <div class="pt-6 border-t border-gray-200">
                    <button type="submit" 
                            id="generateBtn"
                            class="w-full bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-medium py-3 px-6 rounded-lg transition-all duration-200 transform hover:scale-[1.02] focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                        <span class="flex items-center justify-center">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                            Generate Dokumen
                        </span>
                    </button>
                </div>
            </form>
        </div>

        <!-- Info Card -->
        <div class="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div class="flex items-start">
                <svg class="w-5 h-5 text-blue-600 mt-0.5 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <div>
                    <h4 class="text-sm font-medium text-blue-900">Informasi Template</h4>
                    <p class="mt-1 text-sm text-blue-700">
                        Template ini akan menghasilkan surat pernyataan bahwa pegawai tidak sedang menjalani proses hukuman disiplin. 
                        Dokumen akan otomatis tersimpan dengan format nama: <code class="bg-blue-100 px-1 rounded">NIP_PEGAWAI_TEMPLATE_TIMESTAMP.docx</code>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Loading Modal -->
<div id="loadingModal" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg p-6 max-w-sm mx-4">
        <div class="flex items-center">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mr-4"></div>
            <div>
                <h3 class="text-lg font-medium text-gray-900">Generating Document...</h3>
                <p class="text-sm text-gray-600">Please wait while we create your document</p>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
@endpush

@push('scripts')
<script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script>
$(document).ready(function() {
    // Auto-format NIP input
    $('input[name="nip_pegawai"], input[name="nip_pejabat"]').on('input', function() {
        this.value = this.value.replace(/\D/g, '').substring(0, 18);
    });

    // Form submission
    $('#generateForm').on('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        const $btn = $('#generateBtn');
        const originalText = $btn.html();
        
        // Show loading
        $btn.prop('disabled', true).html(`
            <span class="flex items-center justify-center">
                <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Generating...
            </span>
        `);
        $('#loadingModal').removeClass('hidden').addClass('flex');
        
        $.ajax({
            url: '{{ route("templates.pernyataan-hukuman-disiplin.generate") }}',
            method: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                $('#loadingModal').addClass('hidden').removeClass('flex');
                $btn.prop('disabled', false).html(originalText);
                
                if (response.success) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Berhasil!',
                        text: 'Dokumen berhasil di-generate',
                        showCancelButton: true,
                        confirmButtonText: 'Download',
                        cancelButtonText: 'Lihat Daftar',
                        confirmButtonColor: '#3B82F6',
                        cancelButtonColor: '#6B7280'
                    }).then((result) => {
                        if (result.isConfirmed) {
                            window.open(response.download_url, '_blank');
                        } else if (result.dismiss === Swal.DismissReason.cancel) {
                            window.location.href = '{{ route("documents.list") }}';
                        }
                    });
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error!',
                        text: response.message || 'Terjadi kesalahan saat generate dokumen',
                        confirmButtonColor: '#EF4444'
                    });
                }
            },
            error: function(xhr) {
                $('#loadingModal').addClass('hidden').removeClass('flex');
                $btn.prop('disabled', false).html(originalText);
                
                let message = 'Terjadi kesalahan saat generate dokumen';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    message = xhr.responseJSON.message;
                } else if (xhr.responseJSON && xhr.responseJSON.errors) {
                    const errors = Object.values(xhr.responseJSON.errors).flat();
                    message = errors.join(', ');
                }
                
                Swal.fire({
                    icon: 'error',
                    title: 'Error!',
                    text: message,
                    confirmButtonColor: '#EF4444'
                });
            }
        });
    });
});
</script>
@endpush
