<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class GeneratedDocument extends Model
{
    use HasFactory;

    protected $fillable = [
        'pegawai_nip',
        'pegawai_nama',
        'pegawai_golongan',
        'pegawai_jabatan',
        'pegawai_unit_kerja',
        'pejabat_nip',
        'pejabat_nama',
        'pejabat_golongan',
        'pejabat_jabatan',
        'pejabat_unit_kerja',
        'template_name',
        'template_filename',
        'generated_filename',
        'file_path',
        'file_size',
        'generated_by',
        'generated_at',
        'template_variables'
    ];

    protected $casts = [
        'generated_at' => 'datetime',
        'template_variables' => 'array'
    ];

    /**
     * Scope untuk filter berdasarkan NIP pegawai
     */
    public function scopeForPegawai($query, $nip)
    {
        return $query->where('pegawai_nip', $nip);
    }

    /**
     * Scope untuk filter berdasarkan template
     */
    public function scopeForTemplate($query, $templateName)
    {
        return $query->where('template_name', $templateName);
    }

    /**
     * Scope untuk urutkan berdasarkan tanggal generate terbaru
     */
    public function scopeLatest($query)
    {
        return $query->orderBy('generated_at', 'desc');
    }

    /**
     * Get formatted file size
     */
    public function getFormattedFileSizeAttribute()
    {
        $bytes = $this->file_size;
        $units = ['B', 'KB', 'MB', 'GB'];

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * Check if file exists
     */
    public function fileExists()
    {
        return file_exists($this->file_path);
    }

    /**
     * Get download URL
     */
    public function getDownloadUrlAttribute()
    {
        return route('documents.download-document', $this->generated_filename);
    }
}
