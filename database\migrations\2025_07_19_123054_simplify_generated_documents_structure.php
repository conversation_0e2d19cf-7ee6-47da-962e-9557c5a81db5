<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('generated_documents', function (Blueprint $table) {
            // Drop pejabat columns (we'll replace with simpler ones)
            $table->dropColumn([
                'pejabat_nip',
                'pejabat_nama',
                'pejabat_golongan',
                'pejabat_jabatan',
                'pejabat_unit_kerja'
            ]);

            // Add simple pejabat TTD columns
            $table->string('pejabatttd', 500)->nullable()->after('pegawai_unit_kerja');
            $table->string('nippejabatttd', 20)->nullable()->after('pejabatttd');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('generated_documents', function (Blueprint $table) {
            // Drop new columns
            $table->dropColumn(['pejabatttd', 'nippejabatttd']);

            // Restore old pejabat columns
            $table->string('pejabat_nip', 20)->nullable();
            $table->string('pejabat_nama', 500)->nullable();
            $table->string('pejabat_golongan', 50)->nullable();
            $table->text('pejabat_jabatan')->nullable();
            $table->string('pejabat_unit_kerja', 500)->nullable();
        });
    }
};
