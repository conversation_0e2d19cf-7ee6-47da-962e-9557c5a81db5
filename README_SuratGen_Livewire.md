# 📄 Aplikasi Surat Generator (Laravel 12 + Tailwind CSS + Livewire)

Aplikasi web untuk mengelola dan menghasilkan surat otomatis berbasis template Word `.docx`. Dibangun untuk instansi pemerintah seperti Kementerian Agama.

---

## 📝 Alur Utama Aplikasi

1. **Template Surat**
   - Template surat diambil langsung dari folder khusus di server.
   - Terdapat banyak jenis template (bisa lebih dari 8-10 jenis), seluruhnya dapat dipilih saat generate surat.
   - Beberapa template membutuhkan **2 pegawai berbeda**: satu sebagai **pejabat** dan satu sebagai **pegawai biasa**.
2. **Data Pegawai**
   - Hampir semua template membutuhkan data pegawai.
   - Data pegawai **tidak disimpan di database lokal**, melainkan diambil secara _real-time_ dari **API eksternal** (hanya GET, tidak ada POST/PUT/DELETE).
   - Data pegawai berhubungan dengan jenis surat yang digenerate. satu pegawai bisa memiliki lebih dari satu jenis surat.
   - Untuk template yang membutuhkan 2 pegawai, sistem akan mendeteksi placeholder dan menampilkan **2 form pencarian terpisah**:
     - **Form Pencarian Pejabat**: untuk data pejabat (biasanya sebagai penandatangan/pemberi kuasa)
     - **Form Pencarian Pegawai**: untuk data pegawai biasa (biasanya sebagai subjek surat)
3. **Form Generate Surat**
   - Pada form generate surat, field pegawai menggunakan fitur autocomplete (mirip select2, bisa diketikkan NIP/nama).
   - Setelah memilih pegawai, field lain (nama, golongan, jabatan, dst) otomatis terisi dari hasil API.
   - Pengguna cukup mengetikkan NIP/nama, lalu memilih dari hasil pencarian.
   - **Untuk template 2 pegawai**: form akan menampilkan 2 section terpisah dengan label yang jelas (Pejabat dan Pegawai).
4. **Proses Generate & Simpan**
   - **Simpan Sementara**: Data pegawai dari API (NIP, nama, golongan, jabatan, unit kerja, dll) disimpan sementara di session/memory.
   - **Generate Surat**: Data sementara tersebut dijadikan body/isi untuk mengisi placeholder template surat.
   - **Simpan ke Database**: Hasil generate surat beserta data pegawai yang digunakan disimpan ke database sebagai record lengkap.
   - **Download**: File surat `.docx` yang sudah terisi dapat diunduh oleh user.

---

## 🚀 Tech Stack

- Laravel 12 (PHP 8.2+)
- Blade Templating
- Tailwind CSS (tanpa React/Vite, full Tailwind untuk seluruh frontend)
- Laravel Livewire (untuk interaktivitas dinamis)
- PhpWord (manipulasi template `.docx`)
- MySQL / PostgreSQL (Database)
- **API eksternal untuk data pegawai**
- **SweetAlert2** (untuk popup/konfirmasi global)
- **Custom Toast Notifications** (untuk notifikasi berhasil/gagal/info)

---

## 📦 Fitur Utama

- Upload & kelola template surat `.docx` (langsung dari folder)
- Pilihan banyak jenis template
- Menu setting KOP SURAT (isian nanti placeholder)
- Form isian otomatis berdasarkan placeholder
- **Autocomplete data pegawai dari API eksternal** (GET-only)
- **Simpan sementara data pegawai** dari API untuk proses generate
- **Generate surat** dengan data pegawai sebagai body/isi template
- **Simpan hasil generate** ke database sebagai record lengkap
- Hasil surat bisa diunduh dalam format `.docx`
- UI responsif dengan Tailwind CSS
- Struktur bersih berbasis komponen Blade + Livewire
- **Popup konfirmasi dengan SweetAlert2**
- **Custom toast notifications** untuk feedback user

---

## 🎨 UI/UX Components

### SweetAlert2 Integration
- **Global Popup System**: Semua konfirmasi, alert, dan dialog menggunakan SweetAlert2
- **Custom Styling**: Disesuaikan dengan tema pemerintahan
- **Responsive**: Bekerja optimal di mobile dan desktop
- **Accessibility**: Mendukung keyboard navigation dan screen reader

### Custom Toast Notifications
- **Color Variants**: 
  - `bg-gray-800` (Dark) - Default/Info
  - `bg-gray-500` (Gray) - Neutral
  - `bg-teal-500` (Teal) - Success
  - `bg-blue-500` (Blue) - Info
  - `bg-red-500` (Red) - Error
  - `bg-yellow-500` (Yellow) - Warning
- **Auto-dismiss**: Toast otomatis hilang setelah beberapa detik
- **Manual close**: Tombol close untuk dismiss manual
- **Stacking**: Multiple toast bisa ditampilkan bersamaan
- **Responsive**: Adaptif di berbagai ukuran layar

### Toast Usage Examples
```javascript
// Success toast
showToast('Data berhasil disimpan!', 'success');

// Error toast  
showToast('Terjadi kesalahan!', 'error');

// Warning toast
showToast('Periksa kembali data Anda', 'warning');

// Info toast
showToast('Sedang memproses...', 'info');
```

---

## 🛠️ Langkah Setup Otomatis untuk Agent AI (Setelah Project Laravel Siap)

1. Install semua dependency backend menggunakan Composer.
2. Install semua dependency frontend menggunakan npm.
3. Pastikan file konfigurasi environment (`.env`) sudah tersedia dan application key Laravel sudah digenerate.
4. **Konfigurasi API**: Tambahkan konfigurasi API eksternal di `.env`:
   ```env
   # Development
   API_PEGAWAI_DEV=localhost:3000/api
   API_PEGAWAI_AUTH=localhost:3000/api/auth
   
   # Production (uncomment dan ganti dengan URL asli)
   # API_PEGAWAI_DEV=https://api-pegawai.example.com/api
   # API_PEGAWAI_AUTH=https://api-pegawai.example.com/api/auth
   ```
5. **Setup Token Management**: Implementasikan sistem login dan token management untuk API eksternal.
6. **Install Tailwind CSS**: Install Tailwind CSS melalui npm jika belum ada.
7. **Install SweetAlert2**: Install SweetAlert2 untuk popup system.
8. **Konfigurasi Tailwind**: Konfigurasikan file `tailwind.config.js` agar mencakup seluruh folder Blade, JS, dan PHP.
9. **Custom Warna Pemerintahan**: Tambahkan custom warna pada `tailwind.config.js` di bagian `theme.extend.colors` sesuai guideline warna pemerintahan (biru tua, putih, abu-abu terang, aksen kuning/emas/hijau lembut).
10. **Import Direktif Tailwind**: Pastikan direktif Tailwind sudah diimport di file CSS utama (misal: `resources/css/app.css`).
11. **Setup Toast System**: Implementasi custom toast notifications dengan Tailwind.
12. **Setup SweetAlert2**: Konfigurasi SweetAlert2 untuk popup global.
13. **Build Assets**: Build assets Tailwind menggunakan perintah build yang sesuai (misal: `npm run build`).
14. **Migrasi Database**: Migrasikan database jika diperlukan (khusus fitur non-pegawai).
15. **Jalankan Server**: Jalankan server Laravel menggunakan perintah artisan.
16. **Verifikasi Assets**: Pastikan assets frontend sudah terbuild dan perubahan warna/customisasi Tailwind sudah diterapkan.
17. **Tailwind Only**: Semua perubahan warna dan utility dilakukan melalui konfigurasi Tailwind, bukan CSS manual.
18. **Brand Colors**: Jika ingin menambah warna brand instansi, tambahkan di `tailwind.config.js`.
19. **Development Mode**: Untuk development, gunakan perintah build/watch agar perubahan Tailwind langsung terlihat.

---

## 📋 Instruksi Relasi Pegawai & Template untuk Agent AI

- Buat tabel `templates` untuk menyimpan data template surat (misal: nama, path file, dsb) jika belum ada.
- Buat tabel `generated_surats` untuk menyimpan hasil generate surat dengan field:
  - `id` (primary key)
  - `template_id` (foreign key ke templates)
  - `nip_pegawai` (varchar, NIP pegawai dari API)
  - `nip_pejabat` (varchar, NIP pejabat dari API, nullable untuk template 1 pegawai)
  - `data_pegawai` (json, data lengkap pegawai dari API)
  - `data_pejabat` (json, data lengkap pejabat dari API, nullable)
  - `file_path` (varchar, path file surat yang dihasilkan)
  - `created_at`, `updated_at`
- Buat tabel relasi many-to-many (misal: `pegawai_templates`) dengan field minimal: `id`, `nip` (varchar, foreign key ke pegawai), `template_id` (foreign key ke templates), dan field lain jika diperlukan.
- Pastikan satu pegawai bisa memiliki banyak template, dan satu template bisa digunakan oleh banyak pegawai (many-to-many).
- Tambahkan relasi yang sesuai di model (jika model pegawai dan template sudah ada).
- Pastikan migration dan seeder (jika perlu) sudah disiapkan.

---

## 🎨 Desain Frontend (Tailwind CSS)

- Seluruh tampilan frontend wajib menggunakan Tailwind CSS untuk styling.
- Navigasi (navbar) dan footer harus dibuat dalam file Blade terpisah.
- Navigasi dan footer wajib full responsive (mobile, tablet, desktop).
- Warna utama: gunakan nuansa pemerintahan yang clean, modern, dan minimalist.
- Hindari tampilan yang terlalu ramai, prioritaskan keterbacaan dan aksesibilitas.
- Gunakan utility Tailwind untuk spacing, border, shadow, dan responsivitas.
- Pastikan semua komponen (form, tabel, tombol, dsb) konsisten secara visual.
- **SweetAlert2**: Gunakan untuk semua popup/konfirmasi dengan styling yang konsisten.
- **Toast Notifications**: Implementasi custom toast dengan warna yang sesuai (success=teal, error=red, warning=yellow, info=blue).

---

## 🧭 Navigasi & Footer

- Navigasi utama berisi logo, nama aplikasi, dan menu utama.
- Navigasi harus sticky di atas dan collapse menjadi hamburger menu di mobile.
- Footer berisi informasi singkat aplikasi, copyright, dan kontak/tautan penting.
- Navigasi dan footer diletakkan di file Blade terpisah dan di-include di layout utama.

---

## 📁 Struktur Folder

- Template surat `.docx` diletakkan di folder khusus.
- Views utama di `resources/views/`.
- Komponen navigasi dan footer di folder `resources/views/components/`.
- Komponen Livewire di `app/Http/Livewire/`.
- JavaScript utilities di `resources/js/`.
- CSS custom di `resources/css/`.

---

## 🔎 Fitur Autocomplete Pegawai (Livewire + API)

- Komponen pencarian pegawai pada form generate surat menggunakan API eksternal (GET-only).
- **Environment Configuration**: Base URL API diambil dari `.env` untuk switch development/production.
- **Autentikasi API**: Semua request ke API memerlukan token yang valid (TTL 2 jam).
- **Token Management**: Implementasikan sistem login dan token management untuk akses API.
- Pengguna cukup mengetikkan minimal 4 karakter NIP/nama, hasil pencarian akan muncul dan bisa dipilih.
- Setelah pegawai dipilih, field lain (nama, golongan, jabatan, unit kerja, dsb) otomatis terisi dari data API.
- Tidak ada penyimpanan data pegawai di database lokal.
- **Error Handling**: Handle kasus token expired dengan redirect ke login API.
- **Toast Feedback**: Berikan feedback dengan toast untuk setiap aksi (loading, success, error).
- **Untuk template 2 pegawai**: sistem akan menampilkan 2 form pencarian terpisah:
  - **Form Pejabat**: dengan label dan placeholder yang jelas untuk data pejabat
  - **Form Pegawai**: dengan label dan placeholder yang jelas untuk data pegawai biasa
  - Kedua form memiliki autocomplete independen dan field yang terisi otomatis terpisah

---

## 📂 Template Surat

- Semua template surat `.docx` diletakkan di folder khusus.
- Daftar template otomatis terdeteksi dan bisa dipilih saat generate surat (cukup nama template saja) dan dropdown.
- Setiap template bisa memiliki field isian berbeda, namun umumnya membutuhkan data pegawai.
- **Template 2 Pegawai**: beberapa template membutuhkan 2 pegawai berbeda:
  - **Pejabat**: biasanya sebagai penandatangan, pemberi kuasa, atau pejabat yang berwenang
  - **Pegawai Biasa**: biasanya sebagai subjek surat, penerima, atau pegawai yang terkait
  - Sistem akan mendeteksi placeholder dan menyesuaikan form secara dinamis

---

## ✅ Struktur Tabel Pegawai (API)

Tabel pegawai pada API eksternal memiliki struktur sebagai berikut:

| No | Field         | Tipe         | Keterangan      |
|----|--------------|--------------|-----------------|
| 1  | nip          | varchar(20)  | Primary Key     |
| 2  | nama         | varchar(255) |                 |
| 3  | golongan     | varchar(50)  |                 |
| 4  | tmt_pensiun  | date         |                 |
| 5  | unit_kerja   | varchar(255) |                 |
| 6  | induk_unit   | varchar(255) |                 |
| 7  | created_at   | timestamp    |                 |
| 8  | updated_at   | timestamp    |                 |
| 9  | jabatan      | varchar(255) |                 |

Semua field diambil dari API, tidak ada penyimpanan lokal.

---

## ✅ Endpoint API Pegawai (GET Only)

### Base URL
- Gunakan base URL API eksternal yang disediakan.
- **Environment Configuration**: Base URL diambil dari file `.env` untuk memudahkan switch antara development dan production.

### Environment Variables
```env
# Development
API_PEGAWAI_DEV=localhost:3000/api
API_PEGAWAI_AUTH=localhost:3000/api/auth

# Production (ganti dengan URL asli)
# API_PEGAWAI_DEV=https://api-pegawai.example.com/api
# API_PEGAWAI_AUTH=https://api-pegawai.example.com/api/auth
```

### Autentikasi API
- **Login Wajib**: Semua akses ke API memerlukan login terlebih dahulu.
- **Token Management**: Setelah login berhasil, simpan token yang diberikan dengan TTL 2 jam.
- **Auto Refresh**: Implementasikan mekanisme auto refresh token sebelum expired.
- **Session Storage**: Token disimpan di session storage browser untuk keamanan.

### Endpoint Autentikasi

| Method | Endpoint | Keterangan |
|--------|----------|------------|
| POST   | `/api/auth/login` | Login untuk mendapatkan token akses |
| POST   | `/api/auth/refresh` | Refresh token (jika tersedia) |
| POST   | `/api/auth/logout` | Logout dan invalidate token |

### Endpoint Data Pegawai (GET Only)

| Method | Endpoint | Keterangan |
|--------|----------|------------|
| GET    | `/api/pegawai` | List semua pegawai (pagination, filter, search) |
| GET    | `/api/pegawai/{nip}` | Detail pegawai berdasarkan NIP |
| GET    | `/api/pegawai/filter` | Filter pegawai (redirect ke getAllPegawai) |
| GET    | `/api/pegawai/tempat-kerja` | Data pegawai + tempat kerja gabungan |

### Header Authorization
```
Authorization: Bearer {token}
Content-Type: application/json
```

### Catatan Penting

- **Autentikasi Wajib**: Semua request ke API harus menyertakan token di header Authorization.
- **Token TTL**: Token berlaku selama 2 jam, setelah itu harus login ulang atau refresh.
- **Error Handling**: Handle error 401 (Unauthorized) dan 403 (Forbidden) dengan redirect ke login.
- Hanya endpoint GET yang digunakan untuk data pegawai, tidak ada POST/PUT/DELETE.
- NIP digunakan sebagai primary key dan harus unik.
- Format tanggal mengikuti ISO 8601.
- Endpoint mendukung pagination, filter, dan pencarian.
- API mendukung CORS dan error handling konsisten.

---

## 🖼️ UI Design

- Responsive navbar & footer (Blade + Tailwind, file terpisah)
- Form dinamis untuk pengisian surat
- Autocomplete cepat dan ringan (API eksternal, GET-only)
- Tampilan tabel pegawai mengikuti struktur API
- Warna clean, modern, minimalist, dan nuansa pemerintahan
- Tampilan tabel, form, dan komponen lain konsisten dan mudah digunakan
- **SweetAlert2**: Popup konfirmasi yang konsisten dan user-friendly
- **Custom Toast**: Notifikasi yang informatif dan tidak mengganggu
- **Form 2 Pegawai**: untuk template yang membutuhkan 2 pegawai:
  - Section terpisah dengan header yang jelas (Pejabat vs Pegawai)
  - Visual separator atau card untuk membedakan kedua form
  - Label dan placeholder yang deskriptif untuk menghindari kebingungan
  - Responsive layout yang tetap rapi di mobile dan desktop

---

## 📋 TO-DO LIST APLIKASI SURAT GENERATOR

## 🏗️ **PHASE 1: SETUP DASAR & KONFIGURASI**

### 1.1 Setup Environment & Dependencies
- [ ] Install Laravel Livewire
- [ ] Install PhpWord untuk manipulasi .docx
- [ ] Install Tailwind CSS
- [ ] Install SweetAlert2
- [ ] Konfigurasi `.env` dengan API endpoints
- [ ] Setup custom warna pemerintahan di `tailwind.config.js`
- [ ] Build assets Tailwind

### 1.2 Database Setup
- [ ] Buat migration untuk tabel `templates`
- [ ] Buat migration untuk tabel `generated_surats`
- [ ] Buat migration untuk tabel `pegawai_templates` (many-to-many)
- [ ] Buat migration untuk tabel `kop_surat` (setting)
- [ ] Jalankan migration dan seeder

### 1.3 Model & Relasi
- [ ] Buat model `Template`
- [ ] Buat model `GeneratedSurat`
- [ ] Buat model `KopSurat`
- [ ] Setup relasi antar model
- [ ] Buat factory untuk testing

## 🔧 **PHASE 2: API INTEGRATION & AUTHENTICATION**

### 2.1 API Service Layer
- [ ] Buat service class untuk API pegawai
- [ ] Implementasi token management (login, refresh, logout)
- [ ] Setup HTTP client dengan middleware auth
- [ ] Implementasi error handling untuk API
- [ ] Buat cache layer untuk token

### 2.2 API Endpoints Integration
- [ ] Implementasi endpoint `/api/auth/login`
- [ ] Implementasi endpoint `/api/pegawai` (list dengan pagination)
- [ ] Implementasi endpoint `/api/pegawai/{nip}` (detail)
- [ ] Implementasi endpoint `/api/pegawai/filter`
- [ ] Implementasi endpoint `/api/pegawai/tempat-kerja`

## 🎨 **PHASE 3: FRONTEND LAYOUT & COMPONENTS**

### 3.1 Layout Components
- [ ] Buat layout utama (`app.blade.php`)
- [ ] Buat komponen navbar responsive
- [ ] Buat komponen footer
- [ ] Setup routing dasar
- [ ] Implementasi responsive design

### 3.2 UI Components
- [ ] Buat komponen form input dengan Tailwind
- [ ] Buat komponen tabel responsive
- [ ] Buat komponen modal/dialog
- [ ] Buat komponen loading states
- [ ] Buat komponen error handling

### 3.3 Notification System
- [ ] Setup SweetAlert2 global
- [ ] Implementasi custom toast notifications
- [ ] Buat utility functions untuk toast
- [ ] Setup toast container di layout
- [ ] Implementasi auto-dismiss dan manual close

## ⚡ **PHASE 4: LIVEWIRE COMPONENTS**

### 4.1 Autocomplete Components
- [ ] Buat Livewire component `PegawaiSearch`
- [ ] Implementasi autocomplete dengan debounce
- [ ] Setup real-time search ke API
- [ ] Implementasi field auto-fill setelah pilihan
- [ ] Handle error states dan loading
- [ ] Integrasi toast notifications

### 4.2 Form Components
- [ ] Buat Livewire component `SuratGenerator`
- [ ] Implementasi form dinamis berdasarkan template
- [ ] Setup form untuk template 1 pegawai
- [ ] Setup form untuk template 2 pegawai (pejabat + pegawai)
- [ ] Implementasi validasi form
- [ ] Integrasi SweetAlert2 untuk konfirmasi

## 📄 **PHASE 5: TEMPLATE MANAGEMENT**

### 5.1 Template System
- [ ] Buat folder untuk template .docx
- [ ] Implementasi deteksi otomatis template
- [ ] Buat parser untuk placeholder template
- [ ] Setup template validation
- [ ] Implementasi template preview

### 5.2 Template Processing
- [ ] Integrasi PhpWord untuk manipulasi .docx
- [ ] Implementasi placeholder replacement
- [ ] Setup file generation dan storage
- [ ] Implementasi download functionality
- [ ] Setup file cleanup

## 🔧 **PHASE 6: CORE FUNCTIONALITY**

### 6.1 Surat Generation
- [ ] Implementasi proses generate surat
- [ ] Setup data temporary storage (session)
- [ ] Implementasi save ke database
- [ ] Setup file path management
- [ ] Implementasi download surat
- [ ] Integrasi toast feedback

### 6.2 Data Management
- [ ] Implementasi session management untuk data pegawai
- [ ] Setup JSON storage untuk data lengkap
- [ ] Implementasi data validation
- [ ] Setup data cleanup

## 🛠️ **PHASE 7: ADMIN & MANAGEMENT**

### 7.1 Template Management
- [ ] Buat halaman kelola template
- [ ] Implementasi upload template
- [ ] Implementasi delete template
- [ ] Setup template preview
- [ ] Implementasi template validation
- [ ] Integrasi SweetAlert2 untuk konfirmasi delete

### 7.2 Generated Surat Management
- [ ] Buat halaman daftar surat ter-generate
- [ ] Implementasi filter dan search
- [ ] Setup pagination
- [ ] Implementasi download history
- [ ] Setup delete generated surat
- [ ] Integrasi toast notifications

### 7.3 Settings Management
- [ ] Buat halaman setting kop surat
- [ ] Implementasi CRUD kop surat
- [ ] Setup placeholder management
- [ ] Implementasi preview kop surat

## 🧪 **PHASE 8: TESTING & OPTIMIZATION**

### 8.1 Testing
- [ ] Unit test untuk API service
- [ ] Feature test untuk surat generation
- [ ] Browser test untuk Livewire components
- [ ] Test error handling
- [ ] Performance testing

### 8.2 Optimization
- [ ] Implementasi caching untuk API calls
- [ ] Optimize file storage
- [ ] Setup queue untuk heavy operations
- [ ] Implementasi rate limiting
- [ ] Setup monitoring

## 🚀 **PHASE 9: DEPLOYMENT & DOCUMENTATION**

### 9.1 Deployment
- [ ] Setup production environment
- [ ] Konfigurasi production API endpoints
- [ ] Setup file storage di production
- [ ] Implementasi backup strategy
- [ ] Setup SSL dan security

### 9.2 Documentation
- [ ] Buat user manual
- [ ] Buat admin manual
- [ ] Setup API documentation
- [ ] Buat troubleshooting guide
- [ ] Setup changelog

## ✨ **PHASE 10: FINAL POLISHING**

### 10.1 UI/UX Improvements
- [ ] Implementasi loading animations
- [ ] Setup toast notifications
- [ ] Implementasi keyboard shortcuts
- [ ] Setup accessibility features
- [ ] Mobile optimization

### 10.2 Security & Validation
- [ ] Implementasi input sanitization
- [ ] Setup CSRF protection
- [ ] Implementasi file upload validation
- [ ] Setup rate limiting
- [ ] Security audit

---

## 🎯 **PRIORITY ORDER:**
1. **Phase 1-2**: Setup dasar dan API integration (kritis)
2. **Phase 3**: Frontend layout dan notification system (kritis)
3. **Phase 4-6**: Livewire dan core functionality (kritis)
4. **Phase 7**: Admin features (penting)
5. **Phase 8-10**: Testing, optimization, dan polishing (nice-to-have)

