<?php $__env->startSection('title', $templateName); ?>

<?php $__env->startSection('content'); ?>
<div class="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">
    <!-- Header Section -->
    <div class="bg-white shadow-sm border-b border-gray-100">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900"><?php echo e($templateName); ?></h1>
                    <p class="mt-1 text-sm text-gray-600">Generate surat pernyataan tidak sedang menjalani proses hukuman disiplin</p>
                </div>
                <a href="<?php echo e(route('documents.list')); ?>" 
                   class="inline-flex items-center px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 text-sm font-medium rounded-lg transition-colors duration-200">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                    Lihat Dokumen
                </a>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Form Card -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
            <div class="px-6 py-4 bg-gradient-to-r from-blue-600 to-indigo-600">
                <h2 class="text-lg font-semibold text-white">Form Generate Dokumen</h2>
                <p class="text-blue-100 text-sm mt-1">Isi data pegawai dan pejabat penandatangan</p>
            </div>

            <form id="generateForm" class="p-6 space-y-6">
                <?php echo csrf_field(); ?>
                
                <!-- Pegawai Section -->
                <div class="space-y-6">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900">Pilih Pegawai</h3>
                            <p class="text-sm text-gray-600">Cari dan pilih pegawai yang akan dibuatkan surat</p>
                        </div>
                    </div>

                    <!-- Livewire Search Pegawai -->
                    <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-6 border border-blue-200">
                        <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('pegawai-search', [
                            'placeholder' => '🔍 Ketik nama atau NIP pegawai...',
                            'label' => 'Cari Pegawai',
                            'componentId' => 'template-pegawai'
                        ]);

$__html = app('livewire')->mount($__name, $__params, 'lw-714405224-0', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
                    </div>

                    <!-- Keperluan -->
                    <div class="bg-gray-50 rounded-lg p-4">
                        <label for="keperluan" class="block text-sm font-medium text-gray-700 mb-2">
                            📝 Keperluan Surat
                        </label>
                        <input type="text"
                               id="keperluan"
                               name="keperluan"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200"
                               placeholder="Keperluan administrasi (opsional)">
                        <p class="mt-1 text-xs text-gray-500">Kosongkan jika untuk keperluan administrasi umum</p>
                    </div>
                </div>

                <!-- Pejabat Section -->
                <div class="space-y-6 pt-6 border-t border-gray-200">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-gradient-to-r from-green-500 to-green-600 rounded-full flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900">Pilih Pejabat Penandatangan</h3>
                            <p class="text-sm text-gray-600">Cari dan pilih pejabat yang akan menandatangani surat</p>
                        </div>
                    </div>

                    <!-- Livewire Search Pejabat -->
                    <div class="bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-6 border border-green-200">
                        <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('pegawai-search', [
                            'placeholder' => '🔍 Ketik nama atau NIP pejabat...',
                            'label' => 'Cari Pejabat',
                            'componentId' => 'template-pejabat'
                        ]);

$__html = app('livewire')->mount($__name, $__params, 'lw-714405224-1', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
                    </div>
                </div>

                <!-- Hidden inputs for form submission -->
                <input type="hidden" id="nip_pegawai" name="nip_pegawai" required>
                <input type="hidden" id="nip_pejabat" name="nip_pejabat" required>

                <!-- Submit Button -->
                <div class="pt-6 border-t border-gray-200">
                    <button type="submit" 
                            id="generateBtn"
                            class="w-full bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-medium py-3 px-6 rounded-lg transition-all duration-200 transform hover:scale-[1.02] focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                        <span class="flex items-center justify-center">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                            Generate Dokumen
                        </span>
                    </button>
                </div>
            </form>
        </div>

        <!-- Info Card -->
        <div class="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div class="flex items-start">
                <svg class="w-5 h-5 text-blue-600 mt-0.5 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <div>
                    <h4 class="text-sm font-medium text-blue-900">Informasi Template</h4>
                    <p class="mt-1 text-sm text-blue-700">
                        Template ini akan menghasilkan surat pernyataan bahwa pegawai tidak sedang menjalani proses hukuman disiplin. 
                        Dokumen akan otomatis tersimpan dengan format nama: <code class="bg-blue-100 px-1 rounded">NIP_PEGAWAI_TEMPLATE_TIMESTAMP.docx</code>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Loading Modal -->
<div id="loadingModal" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg p-6 max-w-sm mx-4">
        <div class="flex items-center">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mr-4"></div>
            <div>
                <h3 class="text-lg font-medium text-gray-900">Generating Document...</h3>
                <p class="text-sm text-gray-600">Please wait while we create your document</p>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script>
$(document).ready(function() {
    // Store selected pegawai data
    let selectedData = {
        'template-pegawai': null,
        'template-pejabat': null
    };

    // Listen for Livewire pegawai selection events
    document.addEventListener('livewire:init', () => {
        Livewire.on('pegawai-selected', (event) => {
            const data = Array.isArray(event) ? event[0] : event;
            selectedData[data.componentId] = data.pegawai;

            // Update hidden inputs based on component ID
            if (data.componentId === 'template-pegawai') {
                $('#nip_pegawai').val(data.pegawai.nip);
                showToast(`Pegawai berhasil dipilih: ${data.pegawai.nama}`, 'success');
            } else if (data.componentId === 'template-pejabat') {
                $('#nip_pejabat').val(data.pegawai.nip);
                showToast(`Pejabat berhasil dipilih: ${data.pegawai.nama}`, 'success');
            }
        });

        Livewire.on('pegawai-cleared', (event) => {
            const data = Array.isArray(event) ? event[0] : event;
            selectedData[data.componentId] = null;

            // Clear hidden inputs based on component ID
            if (data.componentId === 'template-pegawai') {
                $('#nip_pegawai').val('');
                showToast('Pilihan pegawai telah dihapus', 'info');
            } else if (data.componentId === 'template-pejabat') {
                $('#nip_pejabat').val('');
                showToast('Pilihan pejabat telah dihapus', 'info');
            }
        });
    });

    // Toast notification function
    function showToast(message, type = 'info') {
        const bgColor = type === 'success' ? 'bg-green-500' :
                       type === 'error' ? 'bg-red-500' : 'bg-blue-500';

        const toast = $(`
            <div class="fixed top-4 right-4 ${bgColor} text-white px-6 py-3 rounded-lg shadow-lg z-50 transform translate-x-full transition-transform duration-300">
                <div class="flex items-center space-x-2">
                    <span>${message}</span>
                    <button onclick="$(this).closest('div').remove()" class="ml-2 text-white hover:text-gray-200">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
            </div>
        `);

        $('body').append(toast);

        // Animate in
        setTimeout(() => {
            toast.removeClass('translate-x-full');
        }, 100);

        // Auto remove after 3 seconds
        setTimeout(() => {
            toast.addClass('translate-x-full');
            setTimeout(() => toast.remove(), 300);
        }, 3000);
    }

    // Form submission
    $('#generateForm').on('submit', function(e) {
        e.preventDefault();

        // Validate required fields
        const nipPegawai = $('#nip_pegawai').val();
        const nipPejabat = $('#nip_pejabat').val();

        if (!nipPegawai) {
            Swal.fire({
                icon: 'error',
                title: 'Validasi Error',
                text: 'Silakan pilih pegawai terlebih dahulu',
                confirmButtonColor: '#EF4444'
            });
            return;
        }

        if (!nipPejabat) {
            Swal.fire({
                icon: 'error',
                title: 'Validasi Error',
                text: 'Silakan pilih pejabat penandatangan terlebih dahulu',
                confirmButtonColor: '#EF4444'
            });
            return;
        }

        if (!selectedData['template-pegawai']) {
            Swal.fire({
                icon: 'error',
                title: 'Validasi Error',
                text: 'Data pegawai belum dipilih dengan benar',
                confirmButtonColor: '#EF4444'
            });
            return;
        }

        if (!selectedData['template-pejabat']) {
            Swal.fire({
                icon: 'error',
                title: 'Validasi Error',
                text: 'Data pejabat belum dipilih dengan benar',
                confirmButtonColor: '#EF4444'
            });
            return;
        }

        const formData = new FormData(this);
        const $btn = $('#generateBtn');
        const originalText = $btn.html();
        
        // Show loading
        $btn.prop('disabled', true).html(`
            <span class="flex items-center justify-center">
                <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Generating...
            </span>
        `);
        $('#loadingModal').removeClass('hidden').addClass('flex');
        
        $.ajax({
            url: '<?php echo e(route("templates.pernyataan-hukuman-disiplin.generate")); ?>',
            method: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                $('#loadingModal').addClass('hidden').removeClass('flex');
                $btn.prop('disabled', false).html(originalText);
                
                if (response.success) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Berhasil!',
                        text: 'Dokumen berhasil di-generate',
                        showCancelButton: true,
                        confirmButtonText: 'Download',
                        cancelButtonText: 'Lihat Daftar',
                        confirmButtonColor: '#3B82F6',
                        cancelButtonColor: '#6B7280'
                    }).then((result) => {
                        if (result.isConfirmed) {
                            window.open(response.download_url, '_blank');
                        } else if (result.dismiss === Swal.DismissReason.cancel) {
                            window.location.href = '<?php echo e(route("documents.list")); ?>';
                        }
                    });
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error!',
                        text: response.message || 'Terjadi kesalahan saat generate dokumen',
                        confirmButtonColor: '#EF4444'
                    });
                }
            },
            error: function(xhr) {
                $('#loadingModal').addClass('hidden').removeClass('flex');
                $btn.prop('disabled', false).html(originalText);
                
                let message = 'Terjadi kesalahan saat generate dokumen';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    message = xhr.responseJSON.message;
                } else if (xhr.responseJSON && xhr.responseJSON.errors) {
                    const errors = Object.values(xhr.responseJSON.errors).flat();
                    message = errors.join(', ');
                }
                
                Swal.fire({
                    icon: 'error',
                    title: 'Error!',
                    text: message,
                    confirmButtonColor: '#EF4444'
                });
            }
        });
    });
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Documents\code\mutasites\resources\views/templates/pernyataan-hukuman-disiplin.blade.php ENDPATH**/ ?>