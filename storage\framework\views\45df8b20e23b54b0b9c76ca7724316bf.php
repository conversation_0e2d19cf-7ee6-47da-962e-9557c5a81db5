<?php $__env->startSection('title', $templateName); ?>

<?php $__env->startSection('content'); ?>
<div class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
    <!-- Header Section -->
    <div class="bg-white/90 backdrop-blur-md border-b border-gray-200/50 sticky top-0 z-40 shadow-sm">
        <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <div class="w-14 h-14 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-2xl flex items-center justify-center shadow-lg">
                        <svg class="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                        </svg>
                    </div>
                    <div>
                        <h1 class="text-2xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent">
                            <?php echo e($templateName); ?>

                        </h1>
                        <p class="text-sm text-gray-600 mt-1 flex items-center">
                            <span class="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                            Generate surat pernyataan tidak sedang menjalani proses hukuman disiplin
                        </p>
                    </div>
                </div>
                <div class="flex items-center space-x-3">
                    <a href="<?php echo e(route('documents.index')); ?>" 
                       class="inline-flex items-center px-4 py-2.5 bg-gray-100 hover:bg-gray-200 text-gray-700 text-sm font-medium rounded-xl border border-gray-200 shadow-sm hover:shadow-md transition-all duration-200">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                        Template
                    </a>
                    <a href="<?php echo e(route('documents.list')); ?>" 
                       class="inline-flex items-center px-4 py-2.5 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-xl shadow-lg hover:shadow-xl transition-all duration-200">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                        Lihat Dokumen
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
            <!-- Main Form -->
            <div class="lg:col-span-3">
                <form id="generateForm" method="POST" action="<?php echo e(route('templates.pernyataan-hukuman-disiplin.generate')); ?>" class="space-y-6">
                    <?php echo csrf_field(); ?>

                    <!-- Pegawai Card -->
                    <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 overflow-hidden">
                        <div class="bg-gradient-to-r from-blue-600 to-indigo-600 px-6 py-4">
                            <div class="flex items-center space-x-3">
                                <div class="w-10 h-10 bg-white/20 rounded-xl flex items-center justify-center">
                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <h3 class="text-lg font-semibold text-white">Data Pegawai</h3>
                                    <p class="text-blue-100 text-sm">Pilih pegawai yang akan dibuatkan surat</p>
                                </div>
                            </div>
                        </div>
                        <div class="p-6">
                            <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('pegawai-search', [
                                'placeholder' => '🔍 Ketik nama atau NIP pegawai...',
                                'label' => 'Cari Pegawai',
                                'componentId' => 'template-pegawai'
                            ]);

$__html = app('livewire')->mount($__name, $__params, 'lw-714405224-0', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
                        </div>
                    </div>

                    <!-- Pejabat Card -->
                    <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 overflow-hidden">
                        <div class="bg-gradient-to-r from-emerald-600 to-green-600 px-6 py-4">
                            <div class="flex items-center space-x-3">
                                <div class="w-10 h-10 bg-white/20 rounded-xl flex items-center justify-center">
                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <h3 class="text-lg font-semibold text-white">Pejabat Penandatangan</h3>
                                    <p class="text-emerald-100 text-sm">Pilih pejabat yang akan menandatangani surat</p>
                                </div>
                            </div>
                        </div>
                        <div class="p-6">
                            <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('pegawai-search', [
                                'placeholder' => '🔍 Ketik nama atau NIP pejabat...',
                                'label' => 'Cari Pejabat',
                                'componentId' => 'template-pejabat'
                            ]);

$__html = app('livewire')->mount($__name, $__params, 'lw-714405224-1', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
                        </div>
                    </div>

                    <!-- Additional Info Card -->
                    <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 overflow-hidden">
                        <div class="bg-gradient-to-r from-purple-600 to-pink-600 px-6 py-4">
                            <div class="flex items-center space-x-3">
                                <div class="w-10 h-10 bg-white/20 rounded-xl flex items-center justify-center">
                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <h3 class="text-lg font-semibold text-white">Informasi Tambahan</h3>
                                    <p class="text-purple-100 text-sm">Keperluan dan detail surat</p>
                                </div>
                            </div>
                        </div>
                        <div class="p-6">
                            <label for="keperluan" class="block text-sm font-semibold text-gray-700 mb-3">
                                📝 Keperluan Surat
                            </label>
                            <input type="text" 
                                   id="keperluan" 
                                   name="keperluan" 
                                   class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200 bg-gray-50 hover:bg-white"
                                   placeholder="Keperluan administrasi (opsional)">
                            <p class="mt-2 text-xs text-gray-500 flex items-center">
                                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                Kosongkan jika untuk keperluan administrasi umum
                            </p>
                        </div>
                    </div>

                    <!-- Hidden inputs -->
                    <input type="hidden" id="nip_pegawai" name="nip_pegawai" required>
                    <input type="hidden" id="nip_pejabat" name="nip_pejabat" required>
                </form>
            </div>

            <!-- Sidebar -->
            <div class="lg:col-span-1">
                <div class="sticky top-24 space-y-6">
                    <!-- Progress Card -->
                    <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                            <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                            </svg>
                            Progress
                        </h3>
                        <div class="space-y-3">
                            <div class="flex items-center justify-between text-sm">
                                <span class="text-gray-600">Pegawai</span>
                                <span id="progress-pegawai" class="text-red-500">Belum dipilih</span>
                            </div>
                            <div class="flex items-center justify-between text-sm">
                                <span class="text-gray-600">Pejabat</span>
                                <span id="progress-pejabat" class="text-red-500">Belum dipilih</span>
                            </div>
                            <div class="flex items-center justify-between text-sm">
                                <span class="text-gray-600">Keperluan</span>
                                <span id="progress-keperluan" class="text-gray-400">Opsional</span>
                            </div>
                        </div>
                    </div>

                    <!-- Generate Button -->
                    <button type="submit" 
                            form="generateForm"
                            id="generateBtn"
                            class="w-full bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-semibold py-4 px-6 rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none">
                        <span class="flex items-center justify-center">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                            Generate Dokumen
                        </span>
                    </button>

                    <!-- Info Card -->
                    <div class="bg-gradient-to-r from-amber-50 to-orange-50 rounded-2xl border border-amber-200 p-6">
                        <div class="flex items-start space-x-3">
                            <div class="w-8 h-8 bg-amber-100 rounded-lg flex items-center justify-center flex-shrink-0">
                                <svg class="w-4 h-4 text-amber-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                            <div>
                                <h4 class="text-sm font-semibold text-amber-800 mb-1">Informasi</h4>
                                <p class="text-xs text-amber-700 leading-relaxed">
                                    Pastikan data pegawai dan pejabat sudah benar sebelum generate dokumen. 
                                    Dokumen yang sudah di-generate akan tersimpan di sistem.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Loading Modal -->
<div id="loadingModal" class="fixed inset-0 bg-black/50 backdrop-blur-sm hidden items-center justify-center z-50">
    <div class="bg-white rounded-2xl p-8 max-w-sm mx-4 shadow-2xl">
        <div class="text-center">
            <div class="w-16 h-16 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
            </div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">Generating Document...</h3>
            <p class="text-sm text-gray-600">Mohon tunggu sebentar</p>
        </div>
    </div>
</div>
<?php $__env->startPush('scripts'); ?>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
$(document).ready(function() {
    // Store selected pegawai data
    let selectedData = {
        'template-pegawai': null,
        'template-pejabat': null
    };

    // Update progress indicators
    function updateProgress() {
        const pegawaiStatus = selectedData['template-pegawai'] ?
            '<span class="text-green-500 flex items-center"><svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>Dipilih</span>' :
            '<span class="text-red-500">Belum dipilih</span>';

        const pejabatStatus = selectedData['template-pejabat'] ?
            '<span class="text-green-500 flex items-center"><svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>Dipilih</span>' :
            '<span class="text-red-500">Belum dipilih</span>';

        $('#progress-pegawai').html(pegawaiStatus);
        $('#progress-pejabat').html(pejabatStatus);

        // Update button state
        const canGenerate = selectedData['template-pegawai'] && selectedData['template-pejabat'];
        $('#generateBtn').prop('disabled', !canGenerate);

        if (canGenerate) {
            $('#generateBtn').removeClass('opacity-50 cursor-not-allowed').addClass('hover:scale-105');
        } else {
            $('#generateBtn').addClass('opacity-50 cursor-not-allowed').removeClass('hover:scale-105');
        }
    }

    // Listen for Livewire pegawai selection events
    document.addEventListener('livewire:init', () => {
        Livewire.on('pegawai-selected', (event) => {
            const data = Array.isArray(event) ? event[0] : event;
            selectedData[data.componentId] = data.pegawai;

            // Update hidden inputs based on component ID
            if (data.componentId === 'template-pegawai') {
                $('#nip_pegawai').val(data.pegawai.nip);
                showToast(`✅ Pegawai berhasil dipilih: ${data.pegawai.nama}`, 'success');
            } else if (data.componentId === 'template-pejabat') {
                $('#nip_pejabat').val(data.pegawai.nip);
                showToast(`✅ Pejabat berhasil dipilih: ${data.pegawai.nama}`, 'success');
            }

            updateProgress();
        });

        Livewire.on('pegawai-cleared', (event) => {
            const data = Array.isArray(event) ? event[0] : event;
            selectedData[data.componentId] = null;

            // Clear hidden inputs based on component ID
            if (data.componentId === 'template-pegawai') {
                $('#nip_pegawai').val('');
                showToast('🗑️ Pilihan pegawai telah dihapus', 'info');
            } else if (data.componentId === 'template-pejabat') {
                $('#nip_pejabat').val('');
                showToast('🗑️ Pilihan pejabat telah dihapus', 'info');
            }

            updateProgress();
        });
    });

    // Monitor keperluan input
    $('#keperluan').on('input', function() {
        const value = $(this).val().trim();
        const status = value ?
            '<span class="text-blue-500 flex items-center"><svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path></svg>Diisi</span>' :
            '<span class="text-gray-400">Opsional</span>';
        $('#progress-keperluan').html(status);
    });

    // Modern toast notification
    function showToast(message, type = 'info') {
        const colors = {
            success: 'from-green-500 to-emerald-500',
            error: 'from-red-500 to-pink-500',
            info: 'from-blue-500 to-indigo-500'
        };

        const toast = $(`
            <div class="fixed top-6 right-6 bg-gradient-to-r ${colors[type]} text-white px-6 py-4 rounded-2xl shadow-2xl z-50 transform translate-x-full transition-all duration-300 max-w-sm">
                <div class="flex items-center space-x-3">
                    <div class="flex-1">
                        <p class="text-sm font-medium">${message}</p>
                    </div>
                    <button onclick="$(this).closest('div').addClass('translate-x-full'); setTimeout(() => $(this).closest('div').remove(), 300)" class="text-white/80 hover:text-white">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
            </div>
        `);

        $('body').append(toast);

        // Animate in
        setTimeout(() => {
            toast.removeClass('translate-x-full');
        }, 100);

        // Auto remove after 4 seconds
        setTimeout(() => {
            toast.addClass('translate-x-full');
            setTimeout(() => toast.remove(), 300);
        }, 4000);
    }

    // Form submission with modern loading
    $('#generateForm').on('submit', function(e) {
        e.preventDefault();

        // Validate required fields
        const nipPegawai = $('#nip_pegawai').val();
        const nipPejabat = $('#nip_pejabat').val();

        if (!nipPegawai) {
            Swal.fire({
                icon: 'error',
                title: '❌ Validasi Error',
                text: 'Silakan pilih pegawai terlebih dahulu',
                confirmButtonColor: '#EF4444',
                background: '#fff',
                customClass: {
                    popup: 'rounded-2xl'
                }
            });
            return;
        }

        if (!nipPejabat) {
            Swal.fire({
                icon: 'error',
                title: '❌ Validasi Error',
                text: 'Silakan pilih pejabat penandatangan terlebih dahulu',
                confirmButtonColor: '#EF4444',
                background: '#fff',
                customClass: {
                    popup: 'rounded-2xl'
                }
            });
            return;
        }

        const formData = new FormData(this);
        const $btn = $('#generateBtn');
        const originalText = $btn.html();

        // Show modern loading
        $btn.prop('disabled', true).html(`
            <span class="flex items-center justify-center">
                <div class="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                Generating...
            </span>
        `);
        $('#loadingModal').removeClass('hidden').addClass('flex');

        $.ajax({
            url: '<?php echo e(route("templates.pernyataan-hukuman-disiplin.generate")); ?>',
            method: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                $('#loadingModal').addClass('hidden').removeClass('flex');
                $btn.prop('disabled', false).html(originalText);

                if (response.success) {
                    Swal.fire({
                        icon: 'success',
                        title: '🎉 Berhasil!',
                        text: 'Dokumen berhasil di-generate',
                        confirmButtonText: '📥 Download',
                        showCancelButton: true,
                        cancelButtonText: '📋 Lihat Daftar',
                        confirmButtonColor: '#10B981',
                        cancelButtonColor: '#6B7280',
                        customClass: {
                            popup: 'rounded-2xl'
                        }
                    }).then((result) => {
                        if (result.isConfirmed && response.download_url) {
                            window.location.href = response.download_url;
                        } else if (result.isDismissed) {
                            window.location.href = '<?php echo e(route("documents.list")); ?>';
                        }
                    });
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: '❌ Error',
                        text: response.message || 'Terjadi kesalahan saat generate dokumen',
                        confirmButtonColor: '#EF4444',
                        customClass: {
                            popup: 'rounded-2xl'
                        }
                    });
                }
            },
            error: function(xhr) {
                $('#loadingModal').addClass('hidden').removeClass('flex');
                $btn.prop('disabled', false).html(originalText);

                let errorMessage = 'Terjadi kesalahan saat generate dokumen';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }

                Swal.fire({
                    icon: 'error',
                    title: '❌ Error',
                    text: errorMessage,
                    confirmButtonColor: '#EF4444',
                    customClass: {
                        popup: 'rounded-2xl'
                    }
                });
            }
        });
    });

    // Initialize progress
    updateProgress();
});
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Documents\code\mutasites\resources\views/templates/pernyataan-hukuman-disiplin.blade.php ENDPATH**/ ?>