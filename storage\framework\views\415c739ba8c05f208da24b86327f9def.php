<?php $__env->startSection('title', 'Cari Pegawai'); ?>

<?php $__env->startSection('content'); ?>
<div class="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
    <!-- Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900 mb-2">Pencarian Pegawai</h1>
        <p class="text-gray-600">
            Cari data pegawai berdasarkan NIP atau nama untuk keperluan generate surat
        </p>
    </div>

    <!-- API Status Card -->
    <div class="mb-8 bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between">
            <div>
                <h3 class="text-lg font-medium text-gray-900 mb-2">Status Koneksi API</h3>
                <p class="text-sm text-gray-600">
                    Status koneksi ke API Pegawai dan informasi token
                </p>
            </div>
            <div class="flex items-center space-x-4">
                <div id="api-status-indicator" class="flex items-center space-x-2">
                    <div class="w-3 h-3 bg-gray-400 rounded-full"></div>
                    <span class="text-sm text-gray-500">Checking...</span>
                </div>
                <button
                    onclick="checkApiStatus()"
                    class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 text-sm font-medium"
                >
                    Refresh Status
                </button>
                <button
                    onclick="showLoginModal()"
                    class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-200 text-sm font-medium"
                >
                    Connect to API
                </button>
            </div>
        </div>
        
        <div id="token-info" class="mt-4 p-4 bg-gray-50 rounded-lg hidden">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div>
                    <span class="font-medium text-gray-700">Token Status:</span>
                    <span id="token-valid" class="ml-2 text-gray-900">-</span>
                </div>
                <div>
                    <span class="font-medium text-gray-700">Expires At:</span>
                    <span id="token-expires" class="ml-2 text-gray-900">-</span>
                </div>
                <div>
                    <span class="font-medium text-gray-700">Time Left:</span>
                    <span id="token-time-left" class="ml-2 text-gray-900">-</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Search Forms -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Single Pegawai Search -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="mb-6">
                <h2 class="text-xl font-semibold text-gray-900 mb-2">Pencarian Pegawai Tunggal</h2>
                <p class="text-gray-600 text-sm">
                    Untuk template surat yang membutuhkan data satu pegawai
                </p>
            </div>
            
            <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('pegawai-search', [
                'placeholder' => 'Cari pegawai berdasarkan NIP atau nama...',
                'label' => 'Cari Pegawai',
                'componentId' => 'single-pegawai'
            ]);

$__html = app('livewire')->mount($__name, $__params, 'lw-2243935508-0', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
        </div>

        <!-- Dual Pegawai Search -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="mb-6">
                <h2 class="text-xl font-semibold text-gray-900 mb-2">Pencarian Dua Pegawai</h2>
                <p class="text-gray-600 text-sm">
                    Untuk template surat yang membutuhkan data pejabat dan pegawai
                </p>
            </div>
            
            <!-- Pejabat Search -->
            <div class="mb-6">
                <h3 class="text-lg font-medium text-gray-800 mb-4 flex items-center">
                    <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                    </svg>
                    Data Pejabat
                </h3>
                <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('pegawai-search', [
                    'placeholder' => 'Cari pejabat berdasarkan NIP atau nama...',
                    'label' => 'Cari Pejabat',
                    'componentId' => 'pejabat-pegawai'
                ]);

$__html = app('livewire')->mount($__name, $__params, 'lw-2243935508-1', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
            </div>

            <!-- Pegawai Search -->
            <div>
                <h3 class="text-lg font-medium text-gray-800 mb-4 flex items-center">
                    <svg class="w-5 h-5 mr-2 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                    </svg>
                    Data Pegawai
                </h3>
                <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('pegawai-search', [
                    'placeholder' => 'Cari pegawai berdasarkan NIP atau nama...',
                    'label' => 'Cari Pegawai',
                    'componentId' => 'regular-pegawai'
                ]);

$__html = app('livewire')->mount($__name, $__params, 'lw-2243935508-2', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
            </div>
        </div>
    </div>

    <!-- Selected Data Summary -->
    <div id="selected-summary" class="mt-8 bg-white rounded-lg shadow-sm border border-gray-200 p-6 hidden">
        <h2 class="text-xl font-semibold text-gray-900 mb-4">Ringkasan Data Terpilih</h2>
        <div id="summary-content" class="space-y-4">
            <!-- Content will be populated by JavaScript -->
        </div>
        
        <div class="mt-6 flex space-x-4">
            <button 
                onclick="generateSurat()" 
                class="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 font-medium"
            >
                Generate Surat
            </button>
            <button 
                onclick="clearAllSelections()" 
                class="px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors duration-200 font-medium"
            >
                Clear All
            </button>
        </div>
    </div>

    <!-- Login Modal -->
    <div id="loginModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <!-- Modal Header -->
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">Connect to API Pegawai</h3>
                    <button onclick="hideLoginModal()" class="text-gray-400 hover:text-gray-600 transition-colors duration-200">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <!-- Modal Body -->
                <form id="loginForm" onsubmit="handleLogin(event)">
                    <div class="mb-4">
                        <label for="username" class="block text-sm font-medium text-gray-700 mb-2">Username</label>
                        <input
                            type="text"
                            id="username"
                            name="username"
                            value="admin"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            required
                        />
                    </div>

                    <div class="mb-6">
                        <label for="password" class="block text-sm font-medium text-gray-700 mb-2">Password</label>
                        <input
                            type="password"
                            id="password"
                            name="password"
                            value="admin123"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            required
                        />
                    </div>

                    <!-- Modal Footer -->
                    <div class="flex items-center justify-end space-x-3">
                        <button
                            type="button"
                            onclick="hideLoginModal()"
                            class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors duration-200"
                        >
                            Cancel
                        </button>
                        <button
                            type="submit"
                            id="loginButton"
                            class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors duration-200 flex items-center"
                        >
                            <span id="loginButtonText">Connect</span>
                            <svg id="loginSpinner" class="animate-spin ml-2 h-4 w-4 text-white hidden" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
    // Store selected pegawai data
    let selectedData = {
        'single-pegawai': null,
        'pejabat-pegawai': null,
        'regular-pegawai': null
    };

    // Listen for pegawai selection events
    document.addEventListener('livewire:init', () => {
        Livewire.on('pegawai-selected', (event) => {
            const data = Array.isArray(event) ? event[0] : event;
            selectedData[data.componentId] = data.pegawai;
            updateSummary();
            showToast(`Pegawai berhasil dipilih: ${data.pegawai.nama}`, 'success');
        });

        Livewire.on('pegawai-cleared', (event) => {
            const data = Array.isArray(event) ? event[0] : event;
            selectedData[data.componentId] = null;
            updateSummary();
            showToast('Pilihan pegawai telah dihapus', 'info');
        });
    });

    // Update summary display
    function updateSummary() {
        const summaryDiv = document.getElementById('selected-summary');
        const contentDiv = document.getElementById('summary-content');
        
        const hasSelection = Object.values(selectedData).some(data => data !== null);
        
        if (hasSelection) {
            summaryDiv.classList.remove('hidden');
            
            let content = '';
            
            if (selectedData['single-pegawai']) {
                content += createPegawaiCard('Pegawai Tunggal', selectedData['single-pegawai'], 'blue');
            }
            
            if (selectedData['pejabat-pegawai']) {
                content += createPegawaiCard('Pejabat', selectedData['pejabat-pegawai'], 'purple');
            }
            
            if (selectedData['regular-pegawai']) {
                content += createPegawaiCard('Pegawai', selectedData['regular-pegawai'], 'green');
            }
            
            contentDiv.innerHTML = content;
        } else {
            summaryDiv.classList.add('hidden');
        }
    }

    // Create pegawai card HTML
    function createPegawaiCard(title, pegawai, color) {
        const colors = {
            blue: 'border-blue-200 bg-blue-50',
            purple: 'border-purple-200 bg-purple-50',
            green: 'border-green-200 bg-green-50'
        };
        
        return `
            <div class="border ${colors[color]} rounded-lg p-4">
                <h3 class="font-medium text-gray-900 mb-3">${title}</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                    <div><span class="font-medium">Nama:</span> ${pegawai.nama || 'N/A'}</div>
                    <div><span class="font-medium">NIP:</span> ${pegawai.nip || 'N/A'}</div>
                    <div><span class="font-medium">Golongan:</span> ${pegawai.golongan || 'N/A'}</div>
                    <div><span class="font-medium">Jabatan:</span> ${pegawai.jabatan || 'N/A'}</div>
                    <div class="md:col-span-2"><span class="font-medium">Unit Kerja:</span> ${pegawai.unit_kerja || 'N/A'}</div>
                </div>
            </div>
        `;
    }

    // Check API status
    async function checkApiStatus() {
        const indicator = document.getElementById('api-status-indicator');
        const dot = indicator.querySelector('div');
        const text = indicator.querySelector('span');
        const infoDiv = document.getElementById('token-info');

        // Show loading state
        dot.className = 'w-3 h-3 bg-yellow-400 rounded-full animate-pulse';
        text.textContent = 'Checking...';

        try {
            // Use token manager to get token info
            const data = await window.tokenManager.getTokenInfo();

            if (data.is_valid) {
                dot.className = 'w-3 h-3 bg-green-500 rounded-full animate-pulse';
                text.textContent = 'Connected';
                text.className = 'text-sm text-green-600';
            } else {
                dot.className = 'w-3 h-3 bg-red-500 rounded-full';
                text.textContent = 'Token Invalid';
                text.className = 'text-sm text-red-600';
            }

            // Show token info
            infoDiv.classList.remove('hidden');
            document.getElementById('token-valid').textContent = data.is_valid ? 'Valid' : 'Invalid';
            document.getElementById('token-expires').textContent = data.expires_at ? new Date(data.expires_at).toLocaleString() : 'N/A';
            document.getElementById('token-time-left').textContent = data.seconds_until_expiry ? formatTimeLeft(data.seconds_until_expiry) : 'N/A';
        } catch (error) {
            dot.className = 'w-3 h-3 bg-red-500 rounded-full';
            text.textContent = 'Error';
            text.className = 'text-sm text-red-600';
            showToast('Error checking API status: ' + error.message, 'error');
        }
    }

    // Format time left
    function formatTimeLeft(seconds) {
        if (seconds <= 0) return 'Expired';
        
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = seconds % 60;
        
        return `${hours}h ${minutes}m ${secs}s`;
    }

    // Generate surat (placeholder)
    function generateSurat() {
        showToast('Fitur generate surat akan diimplementasikan selanjutnya', 'info');
        console.log('Selected data for surat generation:', selectedData);
    }

    // Clear all selections
    function clearAllSelections() {
        // Dispatch clear events to all Livewire components
        Livewire.dispatch('clear-all-selections');
        
        // Reset local data
        selectedData = {
            'single-pegawai': null,
            'pejabat-pegawai': null,
            'regular-pegawai': null
        };
        
        updateSummary();
        showToast('Semua pilihan telah dihapus', 'info');
    }

    // Show login modal
    function showLoginModal() {
        document.getElementById('loginModal').classList.remove('hidden');
        document.getElementById('username').focus();
    }

    // Hide login modal
    function hideLoginModal() {
        document.getElementById('loginModal').classList.add('hidden');
        // Reset form
        document.getElementById('loginForm').reset();
        document.getElementById('username').value = 'admin';
        document.getElementById('password').value = 'admin123';
        // Reset button state
        const button = document.getElementById('loginButton');
        const buttonText = document.getElementById('loginButtonText');
        const spinner = document.getElementById('loginSpinner');
        button.disabled = false;
        buttonText.textContent = 'Connect';
        spinner.classList.add('hidden');
    }

    // Handle login form submission
    async function handleLogin(event) {
        event.preventDefault();

        const button = document.getElementById('loginButton');
        const buttonText = document.getElementById('loginButtonText');
        const spinner = document.getElementById('loginSpinner');
        const username = document.getElementById('username').value;
        const password = document.getElementById('password').value;

        // Show loading state
        button.disabled = true;
        buttonText.textContent = 'Connecting...';
        spinner.classList.remove('hidden');

        try {
            // Update credentials in token manager if needed
            const response = await fetch('/api/token/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
                },
                body: JSON.stringify({
                    username: username,
                    password: password
                })
            });

            const data = await response.json();

            if (response.ok && data.success) {
                // Store token data
                if (window.tokenManager) {
                    window.tokenManager.storeTokenData(data.token || 'logged_in', data.expires_at);
                }

                showToast('Successfully connected to API!', 'success');
                hideLoginModal();

                // Refresh status
                setTimeout(() => {
                    checkApiStatus();
                }, 500);
            } else {
                throw new Error(data.message || 'Login failed');
            }
        } catch (error) {
            console.error('Login error:', error);
            showToast('Failed to connect: ' + error.message, 'error');
        } finally {
            // Reset button state
            button.disabled = false;
            buttonText.textContent = 'Connect';
            spinner.classList.add('hidden');
        }
    }

    // Close modal when clicking outside
    document.addEventListener('click', function(event) {
        const modal = document.getElementById('loginModal');
        if (event.target === modal) {
            hideLoginModal();
        }
    });

    // Close modal with Escape key
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape') {
            hideLoginModal();
        }
    });

    // Check API status on page load
    document.addEventListener('DOMContentLoaded', () => {
        checkApiStatus();

        // Auto refresh status every 30 seconds
        setInterval(checkApiStatus, 30000);
    });
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Documents\code\mutasites\resources\views/pegawai/search.blade.php ENDPATH**/ ?>