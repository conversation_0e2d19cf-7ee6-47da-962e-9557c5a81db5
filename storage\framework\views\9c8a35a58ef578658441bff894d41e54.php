<?php $__env->startSection('title', 'Generate Surat'); ?>

<?php $__env->startSection('content'); ?>
<div class="container mx-auto px-4 py-8">
    <div class="max-w-4xl mx-auto">
        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-2">Generate Surat</h1>
            <p class="text-gray-600">Buat surat otomatis menggunakan template dan data pegawai</p>
        </div>

        <!-- Main Form -->
        <div class="bg-white rounded-lg shadow-lg p-6">
            <form id="documentForm" class="space-y-6">
                <?php echo csrf_field(); ?>
                
                <!-- Template Selection -->
                <div>
                    <label for="template" class="block text-sm font-medium text-gray-700 mb-2">
                        Pilih Template Surat
                    </label>
                    <select id="template" name="template" required 
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="">-- Pilih Template --</option>
                        <?php $__currentLoopData = $templates; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $template): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($template['filename']); ?>">
                                <?php echo e($template['name']); ?>

                            </option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>

                <!-- Pejabat Search -->
                <div>
                    <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('pegawai-search', ['label' => 'Pilih Pejabat (Yang Menandatangani)','placeholder' => 'Cari pejabat berdasarkan NIP atau nama...','componentId' => 'document-pejabat-search']);

$__html = app('livewire')->mount($__name, $__params, 'lw-3524830866-0', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
                </div>

                <!-- Pegawai Search -->
                <div>
                    <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('pegawai-search', ['label' => 'Pilih Pegawai (Yang Dimaksud dalam Surat)','placeholder' => 'Cari pegawai berdasarkan NIP atau nama...','componentId' => 'document-pegawai-search']);

$__html = app('livewire')->mount($__name, $__params, 'lw-3524830866-1', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
                </div>

                <!-- Template Variables Preview -->
                <div id="templateVariables" class="hidden">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Preview Template Variables</h3>
                    <p class="text-sm text-gray-600 mb-4">
                        Berikut adalah placeholder yang akan diisi otomatis dari data pegawai yang dipilih:
                    </p>
                    <div id="variablesContainer" class="space-y-4">
                        <!-- Variables will be inserted here -->
                    </div>
                </div>

                <!-- Generate Button -->
                <div class="flex justify-end space-x-4">
                    <button type="button" id="previewVariables"
                            class="px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors duration-200 disabled:opacity-50">
                        Preview Template
                    </button>
                    <button type="submit" id="generateBtn"
                            class="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 disabled:opacity-50">
                        <span class="btn-text">Generate Surat</span>
                        <span class="btn-loading hidden">
                            <svg class="animate-spin h-5 w-5 inline mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            Generating...
                        </span>
                    </button>
                </div>
            </form>
        </div>

        <!-- Result Section -->
        <div id="resultSection" class="hidden mt-8">
            <div class="bg-green-50 border border-green-200 rounded-lg p-6">
                <div class="flex items-center mb-4">
                    <svg class="h-6 w-6 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <h3 class="text-lg font-medium text-green-900">Dokumen Berhasil Dibuat</h3>
                </div>
                <p class="text-green-700 mb-4" id="resultMessage"></p>
                <a id="downloadLink" href="#" 
                   class="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-200">
                    <svg class="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    Download Dokumen
                </a>
            </div>
        </div>

        <!-- Error Section -->
        <div id="errorSection" class="hidden mt-8">
            <div class="bg-red-50 border border-red-200 rounded-lg p-6">
                <div class="flex items-center mb-4">
                    <svg class="h-6 w-6 text-red-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <h3 class="text-lg font-medium text-red-900">Terjadi Kesalahan</h3>
                </div>
                <p class="text-red-700" id="errorMessage"></p>
            </div>
        </div>

        <!-- Templates Info -->
        <div class="mt-8 bg-gray-50 rounded-lg p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Template yang Tersedia</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <?php $__currentLoopData = $templates; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $template): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="bg-white rounded-lg border border-gray-200 p-4">
                        <h4 class="font-medium text-gray-900 mb-2"><?php echo e($template['name']); ?></h4>
                        <div class="text-sm text-gray-600 space-y-1">
                            <p>File: <?php echo e($template['filename']); ?></p>
                            <p>Size: <?php echo e(number_format($template['size'] / 1024, 1)); ?> KB</p>
                            <p>Modified: <?php echo e($template['modified']); ?></p>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('documentForm');
    const templateSelect = document.getElementById('template');
    const generateBtn = document.getElementById('generateBtn');
    const previewBtn = document.getElementById('previewVariables');
    const resultSection = document.getElementById('resultSection');
    const errorSection = document.getElementById('errorSection');
    const templateVariables = document.getElementById('templateVariables');
    const variablesContainer = document.getElementById('variablesContainer');

    let selectedPejabat = null;
    let selectedPegawai = null;

    // Listen for pegawai/pejabat selection
    document.addEventListener('livewire:init', () => {
        Livewire.on('pegawai-selected', (event) => {
            const componentId = event[0].componentId;
            const pegawai = event[0].pegawai;

            if (componentId === 'document-pejabat-search') {
                selectedPejabat = pegawai;
                console.log('Pejabat selected:', selectedPejabat);
            } else if (componentId === 'document-pegawai-search') {
                selectedPegawai = pegawai;
                console.log('Pegawai selected:', selectedPegawai);
            }
        });

        Livewire.on('pegawai-cleared', (event) => {
            const componentId = event[0].componentId;

            if (componentId === 'document-pejabat-search') {
                selectedPejabat = null;
                console.log('Pejabat cleared');
            } else if (componentId === 'document-pegawai-search') {
                selectedPegawai = null;
                console.log('Pegawai cleared');
            }
        });
    });

    // Template selection change
    templateSelect.addEventListener('change', function() {
        if (this.value) {
            loadTemplateVariables(this.value);
        } else {
            templateVariables.classList.add('hidden');
        }
    });

    // Preview variables
    previewBtn.addEventListener('click', function() {
        if (templateSelect.value) {
            loadTemplateVariables(templateSelect.value);
        }
    });

    // Form submission
    form.addEventListener('submit', function(e) {
        e.preventDefault();

        if (!selectedPejabat) {
            showError('Silakan pilih pejabat terlebih dahulu');
            return;
        }

        if (!selectedPegawai) {
            showError('Silakan pilih pegawai terlebih dahulu');
            return;
        }

        if (!templateSelect.value) {
            showError('Silakan pilih template terlebih dahulu');
            return;
        }

        generateDocument();
    });

    function loadTemplateVariables(template) {
        fetch('/documents/template-variables', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({ template: template })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayTemplateVariables(data.variables);
            }
        })
        .catch(error => {
            console.error('Error loading template variables:', error);
        });
    }

    function displayTemplateVariables(variables) {
        variablesContainer.innerHTML = '';

        // Group variables by category
        const pejabatVars = variables.filter(v => v.includes('pejabat'));
        const pegawaiVars = variables.filter(v => v.includes('pegawai'));
        const otherVars = variables.filter(v => !v.includes('pejabat') && !v.includes('pegawai'));

        // Create sections
        if (pejabatVars.length > 0) {
            const pejabatSection = document.createElement('div');
            pejabatSection.className = 'col-span-full';
            pejabatSection.innerHTML = `
                <h4 class="font-medium text-gray-900 mb-2">📋 Data Pejabat (akan diisi otomatis)</h4>
                <div class="bg-blue-50 p-3 rounded-md">
                    <div class="text-sm text-blue-700">
                        ${pejabatVars.map(v => `<span class="inline-block bg-blue-100 px-2 py-1 rounded mr-2 mb-1">{${'{'}${v}}</span>`).join('')}
                    </div>
                </div>
            `;
            variablesContainer.appendChild(pejabatSection);
        }

        if (pegawaiVars.length > 0) {
            const pegawaiSection = document.createElement('div');
            pegawaiSection.className = 'col-span-full';
            pegawaiSection.innerHTML = `
                <h4 class="font-medium text-gray-900 mb-2">👤 Data Pegawai (akan diisi otomatis)</h4>
                <div class="bg-green-50 p-3 rounded-md">
                    <div class="text-sm text-green-700">
                        ${pegawaiVars.map(v => `<span class="inline-block bg-green-100 px-2 py-1 rounded mr-2 mb-1">{${'{'}${v}}</span>`).join('')}
                    </div>
                </div>
            `;
            variablesContainer.appendChild(pegawaiSection);
        }

        if (otherVars.length > 0) {
            const otherSection = document.createElement('div');
            otherSection.className = 'col-span-full';
            otherSection.innerHTML = `
                <h4 class="font-medium text-gray-900 mb-2">🏢 Data Kantor & Dokumen (otomatis)</h4>
                <div class="bg-gray-50 p-3 rounded-md">
                    <div class="text-sm text-gray-700">
                        ${otherVars.map(v => `<span class="inline-block bg-gray-100 px-2 py-1 rounded mr-2 mb-1">{${'{'}${v}}</span>`).join('')}
                    </div>
                </div>
            `;
            variablesContainer.appendChild(otherSection);
        }

        templateVariables.classList.remove('hidden');
    }

    function generateDocument() {
        setLoading(true);
        hideMessages();

        const formData = new FormData(form);
        formData.append('pejabat_nip', selectedPejabat.nip);
        formData.append('pegawai_nip', selectedPegawai.nip);

        fetch('/documents/generate', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            setLoading(false);
            
            if (data.success) {
                showSuccess(data.message, data.download_url, data.file_name);
            } else {
                showError(data.message);
            }
        })
        .catch(error => {
            setLoading(false);
            showError('Terjadi kesalahan saat menghubungi server');
            console.error('Error:', error);
        });
    }

    function setLoading(loading) {
        const btnText = generateBtn.querySelector('.btn-text');
        const btnLoading = generateBtn.querySelector('.btn-loading');
        
        if (loading) {
            btnText.classList.add('hidden');
            btnLoading.classList.remove('hidden');
            generateBtn.disabled = true;
        } else {
            btnText.classList.remove('hidden');
            btnLoading.classList.add('hidden');
            generateBtn.disabled = false;
        }
    }

    function showSuccess(message, downloadUrl, fileName) {
        document.getElementById('resultMessage').textContent = message;
        document.getElementById('downloadLink').href = downloadUrl;
        document.getElementById('downloadLink').download = fileName;
        resultSection.classList.remove('hidden');
        errorSection.classList.add('hidden');
    }

    function showError(message) {
        document.getElementById('errorMessage').textContent = message;
        errorSection.classList.remove('hidden');
        resultSection.classList.add('hidden');
    }

    function hideMessages() {
        resultSection.classList.add('hidden');
        errorSection.classList.add('hidden');
    }
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Documents\code\mutasites\resources\views/documents/index.blade.php ENDPATH**/ ?>