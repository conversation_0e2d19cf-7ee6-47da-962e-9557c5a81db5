<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\TokenController;
use App\Http\Controllers\Api\PegawaiController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

// Token management routes
Route::prefix('token')->group(function () {
    Route::get('/status', [TokenController::class, 'status'])->name('api.token.status');
    Route::post('/refresh', [TokenController::class, 'refresh'])->name('api.token.refresh');
    Route::post('/login', [TokenController::class, 'login'])->name('api.token.login');
    Route::post('/logout', [TokenController::class, 'logout'])->name('api.token.logout');
});

// Pegawai API routes (will be created later)
Route::prefix('pegawai')->group(function () {
    Route::get('/search', [PegawaiController::class, 'search'])->name('api.pegawai.search');
    Route::get('/{nip}', [PegawaiController::class, 'show'])->name('api.pegawai.show');
    Route::get('/', [PegawaiController::class, 'index'])->name('api.pegawai.index');
});
