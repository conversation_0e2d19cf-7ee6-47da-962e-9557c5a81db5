<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('generated_documents', function (Blueprint $table) {
            // Increase column sizes for golongan fields
            $table->string('pegawai_golongan', 50)->nullable()->change();
            $table->string('pejabat_golongan', 50)->nullable()->change();

            // Increase other fields that might be too small
            $table->string('pegawai_nama', 500)->change();
            $table->string('pejabat_nama', 500)->nullable()->change();
            $table->string('pegawai_unit_kerja', 500)->nullable()->change();
            $table->string('pejabat_unit_kerja', 500)->nullable()->change();
            $table->string('template_name', 500)->change();
            $table->string('template_filename', 500)->change();
            $table->string('generated_filename', 500)->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('generated_documents', function (Blueprint $table) {
            $table->string('pegawai_golongan', 10)->nullable()->change();
            $table->string('pejabat_golongan', 10)->nullable()->change();
            $table->string('pegawai_nama', 255)->change();
            $table->string('pejabat_nama', 255)->nullable()->change();
            $table->string('pegawai_unit_kerja', 255)->nullable()->change();
            $table->string('pejabat_unit_kerja', 255)->nullable()->change();
            $table->string('template_name', 255)->change();
            $table->string('template_filename', 255)->change();
            $table->string('generated_filename', 255)->change();
        });
    }
};
