/**
 * Token Manager for API Pegawai
 * Handles token validation and automatic refresh
 */

class TokenManager {
    constructor() {
        this.checkInterval = 30000; // 30 seconds
        this.intervalId = null;
        this.isChecking = false;
        this.callbacks = {
            onTokenExpired: [],
            onTokenRefreshed: [],
            onError: []
        };
    }

    /**
     * Start token monitoring
     */
    start() {
        if (this.intervalId) {
            this.stop();
        }

        console.log('Token Manager: Starting token monitoring...');
        
        // Check immediately
        this.checkTokenStatus();
        
        // Set up interval checking
        this.intervalId = setInterval(() => {
            this.checkTokenStatus();
        }, this.checkInterval);
    }

    /**
     * Stop token monitoring
     */
    stop() {
        if (this.intervalId) {
            clearInterval(this.intervalId);
            this.intervalId = null;
            console.log('Token Manager: Stopped token monitoring');
        }
    }

    /**
     * Check token status from localStorage
     */
    async checkTokenStatus() {
        if (this.isChecking) {
            return;
        }

        this.isChecking = true;

        try {
            const tokenData = this.getStoredTokenData();

            if (!tokenData) {
                // No token stored, try to login
                await this.performLogin();
            } else {
                // Check if token is still valid
                const now = new Date().getTime();
                const expiresAt = new Date(tokenData.expires_at).getTime();
                const secondsUntilExpiry = Math.floor((expiresAt - now) / 1000);

                const status = {
                    has_token: true,
                    is_valid: secondsUntilExpiry > 0,
                    expires_at: tokenData.expires_at,
                    seconds_until_expiry: secondsUntilExpiry
                };

                this.handleTokenStatus(status);
            }
        } catch (error) {
            console.error('Token Manager: Error checking token status', error);
            this.triggerCallback('onError', {
                type: 'check_error',
                error: error.message
            });
        } finally {
            this.isChecking = false;
        }
    }

    /**
     * Get stored token data from localStorage
     */
    getStoredTokenData() {
        try {
            const tokenData = localStorage.getItem('api_pegawai_token');
            return tokenData ? JSON.parse(tokenData) : null;
        } catch (error) {
            console.error('Token Manager: Error parsing stored token', error);
            localStorage.removeItem('api_pegawai_token');
            return null;
        }
    }

    /**
     * Store token data in localStorage
     */
    storeTokenData(token, expiresAt) {
        const tokenData = {
            token: token,
            expires_at: expiresAt,
            stored_at: new Date().toISOString()
        };
        localStorage.setItem('api_pegawai_token', JSON.stringify(tokenData));
    }

    /**
     * Clear stored token data
     */
    clearStoredTokenData() {
        localStorage.removeItem('api_pegawai_token');
    }

    /**
     * Perform login to get new token
     */
    async performLogin() {
        try {
            const response = await fetch('/api/token/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
                }
            });

            if (response.ok) {
                const data = await response.json();
                if (data.success && data.expires_at) {
                    // Store token data
                    this.storeTokenData(data.token || 'logged_in', data.expires_at);

                    const now = new Date().getTime();
                    const expiresAt = new Date(data.expires_at).getTime();
                    const secondsUntilExpiry = Math.floor((expiresAt - now) / 1000);

                    const status = {
                        has_token: true,
                        is_valid: true,
                        expires_at: data.expires_at,
                        seconds_until_expiry: secondsUntilExpiry
                    };

                    this.handleTokenStatus(status);
                    this.triggerCallback('onTokenRefreshed', data);
                    return true;
                }
            }

            throw new Error('Login failed');
        } catch (error) {
            console.error('Token Manager: Login failed', error);
            this.triggerCallback('onError', {
                type: 'login_failed',
                error: error.message
            });
            return false;
        }
    }

    /**
     * Handle token status response
     */
    handleTokenStatus(data) {
        const { has_token, is_valid, seconds_until_expiry } = data;

        if (!has_token || !is_valid) {
            console.warn('Token Manager: Token is invalid or expired');
            this.clearStoredTokenData();
            this.triggerCallback('onTokenExpired', data);
            return;
        }

        // Warn if token expires in less than 5 minutes
        if (seconds_until_expiry && seconds_until_expiry < 300) {
            console.warn(`Token Manager: Token expires in ${seconds_until_expiry} seconds`);
            this.showTokenExpiryWarning(seconds_until_expiry);
        }

        console.log(`Token Manager: Token is valid (expires in ${seconds_until_expiry} seconds)`);
    }

    /**
     * Show token expiry warning
     */
    showTokenExpiryWarning(secondsLeft) {
        const minutes = Math.floor(secondsLeft / 60);
        const seconds = secondsLeft % 60;
        
        if (window.showToast) {
            window.showToast(
                `Token akan expired dalam ${minutes}m ${seconds}s. Sistem akan otomatis login ulang.`,
                'warning'
            );
        }
    }

    /**
     * Attempt to refresh token (same as login for this implementation)
     */
    async refreshToken() {
        return await this.performLogin();
    }

    /**
     * Handle token expiry
     */
    async handleTokenExpiry() {
        console.log('Token Manager: Handling token expiry...');
        
        // Try to refresh token first
        const refreshed = await this.refreshToken();
        
        if (!refreshed) {
            // If refresh fails, redirect to login or show login modal
            if (window.showToast) {
                window.showToast('Sesi telah berakhir. Silakan login ulang.', 'error');
            }
            
            // Redirect to login page after a short delay
            setTimeout(() => {
                window.location.href = '/login';
            }, 2000);
        }
    }

    /**
     * Add callback for events
     */
    on(event, callback) {
        if (this.callbacks[event]) {
            this.callbacks[event].push(callback);
        }
    }

    /**
     * Remove callback for events
     */
    off(event, callback) {
        if (this.callbacks[event]) {
            const index = this.callbacks[event].indexOf(callback);
            if (index > -1) {
                this.callbacks[event].splice(index, 1);
            }
        }
    }

    /**
     * Trigger callbacks for an event
     */
    triggerCallback(event, data) {
        if (this.callbacks[event]) {
            this.callbacks[event].forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error(`Token Manager: Error in ${event} callback`, error);
                }
            });
        }
    }

    /**
     * Get current token info from localStorage
     */
    async getTokenInfo() {
        const tokenData = this.getStoredTokenData();

        if (!tokenData) {
            return {
                has_token: false,
                is_valid: false,
                expires_at: null,
                seconds_until_expiry: null
            };
        }

        const now = new Date().getTime();
        const expiresAt = new Date(tokenData.expires_at).getTime();
        const secondsUntilExpiry = Math.floor((expiresAt - now) / 1000);

        return {
            has_token: true,
            is_valid: secondsUntilExpiry > 0,
            expires_at: tokenData.expires_at,
            seconds_until_expiry: secondsUntilExpiry
        };
    }
}

// Create global instance
window.tokenManager = new TokenManager();

// Set up event handlers
window.tokenManager.on('onTokenExpired', (data) => {
    console.log('Token expired event triggered', data);
    window.tokenManager.handleTokenExpiry();
});

window.tokenManager.on('onError', (data) => {
    console.error('Token manager error', data);
});

// Auto-start when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Start token monitoring
    window.tokenManager.start();
    
    // Stop monitoring when page is about to unload
    window.addEventListener('beforeunload', () => {
        window.tokenManager.stop();
    });
});

export default TokenManager;
