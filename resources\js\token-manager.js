/**
 * Token Manager for API Pegawai
 * Handles token validation and automatic refresh
 */

class TokenManager {
    constructor() {
        this.checkInterval = 30000; // 30 seconds
        this.intervalId = null;
        this.isChecking = false;
        this.callbacks = {
            onTokenExpired: [],
            onTokenRefreshed: [],
            onError: []
        };
    }

    /**
     * Start token monitoring
     */
    start() {
        if (this.intervalId) {
            this.stop();
        }

        console.log('Token Manager: Starting token monitoring...');
        
        // Check immediately
        this.checkTokenStatus();
        
        // Set up interval checking
        this.intervalId = setInterval(() => {
            this.checkTokenStatus();
        }, this.checkInterval);
    }

    /**
     * Stop token monitoring
     */
    stop() {
        if (this.intervalId) {
            clearInterval(this.intervalId);
            this.intervalId = null;
            console.log('Token Manager: Stopped token monitoring');
        }
    }

    /**
     * Check token status via API
     */
    async checkTokenStatus() {
        if (this.isChecking) {
            return;
        }

        this.isChecking = true;

        try {
            const response = await fetch('/api/token-status', {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
                }
            });

            if (response.ok) {
                const data = await response.json();
                this.handleTokenStatus(data);
            } else {
                console.error('Token Manager: Failed to check token status', response.status);
                this.triggerCallback('onError', { 
                    type: 'check_failed', 
                    status: response.status 
                });
            }
        } catch (error) {
            console.error('Token Manager: Error checking token status', error);
            this.triggerCallback('onError', { 
                type: 'network_error', 
                error: error.message 
            });
        } finally {
            this.isChecking = false;
        }
    }

    /**
     * Handle token status response
     */
    handleTokenStatus(data) {
        const { has_token, is_valid, seconds_until_expiry } = data;

        if (!has_token || !is_valid) {
            console.warn('Token Manager: Token is invalid or expired');
            this.triggerCallback('onTokenExpired', data);
            return;
        }

        // Warn if token expires in less than 5 minutes
        if (seconds_until_expiry && seconds_until_expiry < 300) {
            console.warn(`Token Manager: Token expires in ${seconds_until_expiry} seconds`);
            this.showTokenExpiryWarning(seconds_until_expiry);
        }

        console.log(`Token Manager: Token is valid (expires in ${seconds_until_expiry} seconds)`);
    }

    /**
     * Show token expiry warning
     */
    showTokenExpiryWarning(secondsLeft) {
        const minutes = Math.floor(secondsLeft / 60);
        const seconds = secondsLeft % 60;
        
        if (window.showToast) {
            window.showToast(
                `Token akan expired dalam ${minutes}m ${seconds}s. Sistem akan otomatis login ulang.`,
                'warning'
            );
        }
    }

    /**
     * Attempt to refresh token
     */
    async refreshToken() {
        try {
            const response = await fetch('/api/refresh-token', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
                }
            });

            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    console.log('Token Manager: Token refreshed successfully');
                    this.triggerCallback('onTokenRefreshed', data);
                    
                    if (window.showToast) {
                        window.showToast('Token berhasil diperbaharui', 'success');
                    }
                    
                    return true;
                }
            }

            console.error('Token Manager: Failed to refresh token');
            return false;
        } catch (error) {
            console.error('Token Manager: Error refreshing token', error);
            return false;
        }
    }

    /**
     * Handle token expiry
     */
    async handleTokenExpiry() {
        console.log('Token Manager: Handling token expiry...');
        
        // Try to refresh token first
        const refreshed = await this.refreshToken();
        
        if (!refreshed) {
            // If refresh fails, redirect to login or show login modal
            if (window.showToast) {
                window.showToast('Sesi telah berakhir. Silakan login ulang.', 'error');
            }
            
            // Redirect to login page after a short delay
            setTimeout(() => {
                window.location.href = '/login';
            }, 2000);
        }
    }

    /**
     * Add callback for events
     */
    on(event, callback) {
        if (this.callbacks[event]) {
            this.callbacks[event].push(callback);
        }
    }

    /**
     * Remove callback for events
     */
    off(event, callback) {
        if (this.callbacks[event]) {
            const index = this.callbacks[event].indexOf(callback);
            if (index > -1) {
                this.callbacks[event].splice(index, 1);
            }
        }
    }

    /**
     * Trigger callbacks for an event
     */
    triggerCallback(event, data) {
        if (this.callbacks[event]) {
            this.callbacks[event].forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error(`Token Manager: Error in ${event} callback`, error);
                }
            });
        }
    }

    /**
     * Get current token info
     */
    async getTokenInfo() {
        try {
            const response = await fetch('/api/token-status', {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
                }
            });

            if (response.ok) {
                return await response.json();
            }
        } catch (error) {
            console.error('Token Manager: Error getting token info', error);
        }
        
        return null;
    }
}

// Create global instance
window.tokenManager = new TokenManager();

// Set up event handlers
window.tokenManager.on('onTokenExpired', (data) => {
    console.log('Token expired event triggered', data);
    window.tokenManager.handleTokenExpiry();
});

window.tokenManager.on('onError', (data) => {
    console.error('Token manager error', data);
});

// Auto-start when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Start token monitoring
    window.tokenManager.start();
    
    // Stop monitoring when page is about to unload
    window.addEventListener('beforeunload', () => {
        window.tokenManager.stop();
    });
});

export default TokenManager;
