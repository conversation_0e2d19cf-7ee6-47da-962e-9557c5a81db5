<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Services\PegawaiApiService;

class PegawaiController extends Controller
{
    private $pegawaiApiService;

    public function __construct(PegawaiApiService $pegawaiApiService)
    {
        $this->pegawaiApiService = $pegawaiApiService;
    }

    /**
     * Show the pegawai search page
     */
    public function search()
    {
        return view('pegawai.search');
    }

    /**
     * Show pegawai details
     */
    public function show($nip)
    {
        try {
            $result = $this->pegawaiApiService->getPegawaiByNip($nip);
            
            if ($result['success']) {
                $pegawai = $result['data'];
                return view('pegawai.show', compact('pegawai'));
            }

            return redirect()->route('pegawai.search')
                ->with('error', $result['message'] ?? 'Pegawai tidak ditemukan');
        } catch (\Exception $e) {
            return redirect()->route('pegawai.search')
                ->with('error', '<PERSON><PERSON><PERSON><PERSON> k<PERSON>: ' . $e->getMessage());
        }
    }
}
