<?php

namespace App\Services;

use App\Http\Controllers\Templates\PernyataanHukumanDisiplinController;

class TemplateFactory
{
    /**
     * Available templates configuration
     */
    protected static $templates = [
        'pernyataan-hukuman-disiplin' => [
            'name' => 'Pernyataan Tidak Sedang Menjalani Proses Hukuman Disiplin',
            'description' => 'Surat pernyataan bahwa pegawai tidak sedang men<PERSON>lani proses hukuman disiplin',
            'controller' => PernyataanHukumanDisiplinController::class,
            'route' => 'templates.pernyataan-hukuman-disiplin.index',
            'icon' => 'shield-check',
            'category' => 'Administrasi',
            'file' => 'KEMENAG_PERNYATAAN TIDAK SEDANG MENALANI PROSES HUKUMAN DISIPLIN.docx'
        ],
        // Template baru bisa ditambahkan di sini
        /*
        'surat-tugas' => [
            'name' => 'Surat Tugas',
            'description' => 'Surat penugasan untuk pegawai',
            'controller' => SuratTugasController::class,
            'route' => 'templates.surat-tugas.index',
            'icon' => 'clipboard-list',
            'category' => 'Penugasan',
            'file' => 'TEMPLATE_SURAT_TUGAS.docx'
        ],
        */
    ];

    /**
     * Get all available templates
     */
    public static function getAllTemplates()
    {
        return self::$templates;
    }

    /**
     * Get template by key
     */
    public static function getTemplate($key)
    {
        return self::$templates[$key] ?? null;
    }

    /**
     * Get templates by category
     */
    public static function getTemplatesByCategory($category = null)
    {
        if (!$category) {
            return self::$templates;
        }

        return array_filter(self::$templates, function($template) use ($category) {
            return $template['category'] === $category;
        });
    }

    /**
     * Get all categories
     */
    public static function getCategories()
    {
        $categories = [];
        foreach (self::$templates as $template) {
            if (!in_array($template['category'], $categories)) {
                $categories[] = $template['category'];
            }
        }
        return $categories;
    }

    /**
     * Register new template
     */
    public static function registerTemplate($key, $config)
    {
        self::$templates[$key] = $config;
    }

    /**
     * Check if template exists
     */
    public static function templateExists($key)
    {
        return isset(self::$templates[$key]);
    }

    /**
     * Get template controller instance
     */
    public static function getController($key)
    {
        $template = self::getTemplate($key);
        if (!$template || !$template['controller']) {
            return null;
        }

        return app($template['controller']);
    }

    /**
     * Generate template cards for UI
     */
    public static function generateTemplateCards()
    {
        $cards = [];
        foreach (self::$templates as $key => $template) {
            $cards[] = [
                'key' => $key,
                'name' => $template['name'],
                'description' => $template['description'],
                'route' => route($template['route']),
                'icon' => $template['icon'],
                'category' => $template['category']
            ];
        }
        return $cards;
    }
}
