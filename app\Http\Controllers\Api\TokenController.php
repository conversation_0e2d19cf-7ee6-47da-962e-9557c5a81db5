<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\PegawaiApiService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class TokenController extends Controller
{
    private $pegawaiApiService;

    public function __construct(PegawaiApiService $pegawaiApiService)
    {
        $this->pegawaiApiService = $pegawaiApiService;
    }

    /**
     * Get current token status
     */
    public function status(): JsonResponse
    {
        try {
            $tokenInfo = $this->pegawaiApiService->getTokenInfo();
            
            return response()->json([
                'success' => true,
                'has_token' => $tokenInfo['has_token'],
                'is_valid' => $tokenInfo['is_valid'],
                'expires_at' => $tokenInfo['expires_at'],
                'seconds_until_expiry' => $tokenInfo['seconds_until_expiry']
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get token status: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Refresh token by logging in again
     */
    public function refresh(): JsonResponse
    {
        try {
            $result = $this->pegawaiApiService->login();
            
            if ($result['success']) {
                return response()->json([
                    'success' => true,
                    'message' => 'Token refreshed successfully',
                    'expires_at' => $result['expires_at']
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => $result['message'] ?? 'Failed to refresh token'
            ], 401);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to refresh token: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Login to API
     */
    public function login(Request $request): JsonResponse
    {
        try {
            // Get credentials from request
            $username = $request->input('username');
            $password = $request->input('password');

            // Log for debugging
            \Log::info('Login attempt', [
                'username' => $username,
                'has_password' => !empty($password),
                'request_data' => $request->all()
            ]);

            if (empty($username) || empty($password)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Username and password are required'
                ], 400);
            }

            $result = $this->pegawaiApiService->login($username, $password);

            if ($result['success']) {
                return response()->json([
                    'success' => true,
                    'message' => 'Login successful',
                    'token' => $result['token'] ?? null,
                    'expires_at' => $result['expires_at']
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => $result['message'] ?? 'Login failed'
            ], 401);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Login failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Logout from API
     */
    public function logout(): JsonResponse
    {
        try {
            $result = $this->pegawaiApiService->logout();
            
            return response()->json([
                'success' => true,
                'message' => 'Logout successful'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Logout failed: ' . $e->getMessage()
            ], 500);
        }
    }
}
