<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ config('app.name', 'Laravel') }} - @yield('title', 'Aplikasi Surat Generator')</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=instrument-sans:400,500,600" rel="stylesheet" />

    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])
    @livewireStyles
</head>
<body class="bg-gray-50 font-sans antialiased">
    <!-- Navigation -->
    <nav class="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-40">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <!-- Logo -->
                    <div class="flex-shrink-0 flex items-center">
                        <h1 class="text-xl font-bold text-blue-900">
                            {{ config('app.name', 'Surat Generator') }}
                        </h1>
                    </div>

                    <!-- Navigation Links -->
                    <div class="hidden md:ml-8 md:flex md:space-x-8">
                        <a href="{{ route('pegawai.search') }}"
                           class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200 {{ request()->routeIs('pegawai.*') ? 'border-blue-500 text-blue-600' : '' }}">
                            Cari Pegawai
                        </a>
                        <a href="{{ route('documents.index') }}"
                           class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200 {{ request()->routeIs('documents.index') ? 'border-blue-500 text-blue-600' : '' }}">
                            Generate Surat
                        </a>
                        <a href="{{ route('documents.list') }}"
                           class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200 {{ request()->routeIs('documents.list') ? 'border-blue-500 text-blue-600' : '' }}">
                            Daftar Dokumen
                        </a>
                    </div>
                </div>

                <!-- Token Status -->
                <div class="flex items-center space-x-4">
                    <div id="token-status" class="hidden md:flex items-center space-x-2">
                        <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                        <span class="text-sm text-gray-600">API Connected</span>
                    </div>

                    <!-- Mobile menu button -->
                    <div class="md:hidden">
                        <button type="button" 
                                class="bg-white inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500"
                                onclick="toggleMobileMenu()">
                            <span class="sr-only">Open main menu</span>
                            <svg class="block h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Mobile menu -->
        <div id="mobile-menu" class="hidden md:hidden">
            <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t border-gray-200">
                <a href="{{ route('pegawai.search') }}" 
                   class="text-gray-500 hover:text-gray-700 block px-3 py-2 rounded-md text-base font-medium transition-colors duration-200">
                    Cari Pegawai
                </a>
                <a href="#" 
                   class="text-gray-500 hover:text-gray-700 block px-3 py-2 rounded-md text-base font-medium transition-colors duration-200">
                    Generate Surat
                </a>
                <a href="#" 
                   class="text-gray-500 hover:text-gray-700 block px-3 py-2 rounded-md text-base font-medium transition-colors duration-200">
                    Template
                </a>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="min-h-screen">
        @yield('content')
    </main>

    <!-- Footer -->
    <footer class="bg-white border-t border-gray-200 mt-12">
        <div class="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
            <div class="text-center">
                <p class="text-gray-500 text-sm">
                    &copy; {{ date('Y') }} {{ config('app.name', 'Surat Generator') }}. 
                    Aplikasi Surat Generator untuk Instansi Pemerintah.
                </p>
                <p class="text-gray-400 text-xs mt-2">
                    Dibangun dengan Laravel {{ app()->version() }} & Tailwind CSS
                </p>
            </div>
        </div>
    </footer>

    <!-- Toast Container -->
    <div id="toast-container" class="fixed top-4 right-4 z-50 space-y-2"></div>

    <!-- Scripts -->
    @livewireScripts
    
    <script>
        // Mobile menu toggle
        function toggleMobileMenu() {
            const menu = document.getElementById('mobile-menu');
            menu.classList.toggle('hidden');
        }

        // Toast notification system
        window.showToast = function(message, type = 'info', duration = 5000) {
            const container = document.getElementById('toast-container');
            const toast = document.createElement('div');
            
            const colors = {
                success: 'bg-green-500',
                error: 'bg-red-500',
                warning: 'bg-yellow-500',
                info: 'bg-blue-500'
            };
            
            const icons = {
                success: '✓',
                error: '✕',
                warning: '⚠',
                info: 'ℹ'
            };
            
            toast.className = `${colors[type] || colors.info} text-white px-6 py-4 rounded-lg shadow-lg transform transition-all duration-300 translate-x-full opacity-0 max-w-sm`;
            toast.innerHTML = `
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <span class="text-lg mr-3">${icons[type] || icons.info}</span>
                        <span class="text-sm font-medium">${message}</span>
                    </div>
                    <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-white hover:text-gray-200 transition-colors duration-200">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
            `;
            
            container.appendChild(toast);
            
            // Animate in
            setTimeout(() => {
                toast.classList.remove('translate-x-full', 'opacity-0');
            }, 100);
            
            // Auto remove
            setTimeout(() => {
                toast.classList.add('translate-x-full', 'opacity-0');
                setTimeout(() => {
                    if (toast.parentElement) {
                        toast.remove();
                    }
                }, 300);
            }, duration);
        };

        // Listen for Livewire toast events
        document.addEventListener('livewire:init', () => {
            Livewire.on('show-toast', (event) => {
                const data = Array.isArray(event) ? event[0] : event;
                showToast(data.message, data.type || 'info');
            });
        });

        // Token status monitoring
        function updateTokenStatus(isValid) {
            const statusElement = document.getElementById('token-status');
            const dot = statusElement.querySelector('div');
            const text = statusElement.querySelector('span');
            
            if (isValid) {
                dot.className = 'w-2 h-2 bg-green-500 rounded-full animate-pulse';
                text.textContent = 'API Connected';
                text.className = 'text-sm text-gray-600';
            } else {
                dot.className = 'w-2 h-2 bg-red-500 rounded-full';
                text.textContent = 'API Disconnected';
                text.className = 'text-sm text-red-600';
            }
        }

        // Listen for token manager events
        if (window.tokenManager) {
            window.tokenManager.on('onTokenExpired', () => {
                updateTokenStatus(false);
                showToast('Token API telah expired. Mencoba login ulang...', 'warning');
            });

            window.tokenManager.on('onTokenRefreshed', () => {
                updateTokenStatus(true);
                showToast('Token API berhasil diperbaharui', 'success');
            });

            window.tokenManager.on('onError', (data) => {
                updateTokenStatus(false);
                showToast('Error koneksi API: ' + (data.error || 'Unknown error'), 'error');
            });
        }
    </script>

    @stack('scripts')
</body>
</html>
