@extends('layouts.app')

@section('title', 'Daftar Dokumen Pegawai')

@section('content')
<div class="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50">
    <!-- Header Section -->
    <div class="bg-white shadow-sm border-b border-gray-100">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">📋 Daftar Dokumen Pegawai</h1>
                    <p class="mt-2 text-gray-600">Kelola dan pantau dokumen yang telah di-generate untuk setiap pegawai</p>
                </div>
                <div class="flex flex-col sm:flex-row gap-3">
                    <a href="{{ route('documents.index') }}"
                       class="inline-flex items-center justify-center px-4 py-2 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white text-sm font-medium rounded-lg transition-all duration-200 transform hover:scale-105 shadow-lg">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        Generate Dokumen Baru
                    </a>
                    <button onclick="refreshTable()"
                            class="inline-flex items-center justify-center px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 text-sm font-medium rounded-lg transition-colors duration-200">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                        </svg>
                        Refresh
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                <div class="flex items-center">
                    <div class="p-3 bg-blue-100 rounded-full">
                        <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Total Pegawai</p>
                        <p class="text-2xl font-bold text-gray-900" id="totalPegawai">-</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                <div class="flex items-center">
                    <div class="p-3 bg-green-100 rounded-full">
                        <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Total Dokumen</p>
                        <p class="text-2xl font-bold text-gray-900" id="totalDokumen">-</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                <div class="flex items-center">
                    <div class="p-3 bg-purple-100 rounded-full">
                        <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Hari Ini</p>
                        <p class="text-2xl font-bold text-gray-900" id="dokumenHariIni">-</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- DataTable Card -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
            <div class="px-6 py-4 bg-gradient-to-r from-gray-50 to-gray-100 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-900">Data Pegawai & Dokumen</h2>
                <p class="text-sm text-gray-600 mt-1">Daftar pegawai yang memiliki dokumen yang telah di-generate</p>
            </div>

            <div class="p-6">
                <table id="pegawaiDocumentsTable" class="w-full">
                    <thead>
                        <tr>
                            <th class="px-6 py-4 text-left text-sm font-semibold text-gray-800 bg-gray-50 border-b border-gray-200">
                                <div class="flex items-center space-x-2">
                                    <span class="text-blue-600">🆔</span>
                                    <span>NIP</span>
                                </div>
                            </th>
                            <th class="px-6 py-4 text-left text-sm font-semibold text-gray-800 bg-gray-50 border-b border-gray-200">
                                <div class="flex items-center space-x-2">
                                    <span class="text-green-600">👤</span>
                                    <span>Nama Pegawai</span>
                                </div>
                            </th>
                            <th class="px-6 py-4 text-left text-sm font-semibold text-gray-800 bg-gray-50 border-b border-gray-200">
                                <div class="flex items-center space-x-2">
                                    <span class="text-purple-600">🏅</span>
                                    <span>Golongan</span>
                                </div>
                            </th>
                            <th class="px-6 py-4 text-left text-sm font-semibold text-gray-800 bg-gray-50 border-b border-gray-200">
                                <div class="flex items-center space-x-2">
                                    <span class="text-orange-600">💼</span>
                                    <span>Jabatan</span>
                                </div>
                            </th>
                            <th class="px-6 py-4 text-left text-sm font-semibold text-gray-800 bg-gray-50 border-b border-gray-200">
                                <div class="flex items-center space-x-2">
                                    <span class="text-indigo-600">🏢</span>
                                    <span>Unit Kerja</span>
                                </div>
                            </th>
                            <th class="px-6 py-4 text-center text-sm font-semibold text-gray-800 bg-gray-50 border-b border-gray-200">
                                <div class="flex items-center justify-center space-x-2">
                                    <span class="text-red-600">📄</span>
                                    <span>Dokumen</span>
                                </div>
                            </th>
                            <th class="px-6 py-4 text-center text-sm font-semibold text-gray-800 bg-gray-50 border-b border-gray-200">
                                <div class="flex items-center justify-center space-x-2">
                                    <span class="text-gray-600">⚡</span>
                                    <span>Aksi</span>
                                </div>
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <!-- Data will be loaded via DataTables -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Loading Modal -->
<div id="loadingModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
    <div class="bg-white rounded-xl p-6 max-w-sm mx-4 shadow-2xl">
        <div class="flex items-center">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mr-4"></div>
            <div>
                <h3 class="text-lg font-medium text-gray-900">Memuat Data...</h3>
                <p class="text-sm text-gray-600">Mohon tunggu sebentar</p>
            </div>
        </div>
    </div>
</div>

<!-- Documents Modal -->
<div id="documentsModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
    <div class="bg-white rounded-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden shadow-2xl">
        <!-- Modal Header -->
        <div class="px-6 py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-lg font-semibold">📄 Dokumen Pegawai</h3>
                    <p class="text-blue-100 text-sm" id="modalPegawaiInfo"></p>
                </div>
                <button onclick="closeDocumentsModal()" class="text-white hover:text-gray-200 transition-colors">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
        </div>

        <!-- Modal Body -->
        <div class="p-6 max-h-[70vh] overflow-y-auto">
            <div id="documentsContent" class="space-y-4">
                <!-- Documents will be loaded here -->
            </div>
        </div>

        <!-- Modal Footer -->
        <div class="px-6 py-4 bg-gray-50 border-t border-gray-200">
            <div class="flex justify-end">
                <button onclick="closeDocumentsModal()"
                        class="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white text-sm font-medium rounded-lg transition-colors">
                    Tutup
                </button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<!-- DataTables CSS -->
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.13.6/css/dataTables.tailwindcss.min.css">
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/responsive/2.5.0/css/responsive.dataTables.min.css">
<!-- SweetAlert2 CSS -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
@endpush

@push('scripts')
<!-- jQuery -->
<script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
<!-- SweetAlert2 JS -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<!-- DataTables JS -->
<script type="text/javascript" src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
<script type="text/javascript" src="https://cdn.datatables.net/1.13.6/js/dataTables.tailwindcss.min.js"></script>
<script type="text/javascript" src="https://cdn.datatables.net/responsive/2.5.0/js/dataTables.responsive.min.js"></script>

<script>
$(document).ready(function() {
    // Show loading modal
    function showLoading() {
        $('#loadingModal').removeClass('hidden').addClass('flex');
    }
    
    // Hide loading modal
    function hideLoading() {
        $('#loadingModal').addClass('hidden').removeClass('flex');
    }

    // Initialize DataTable
    const table = $('#pegawaiDocumentsTable').DataTable({
        processing: true,
        serverSide: true,
        responsive: true,
        pageLength: 25,
        language: {
            processing: '<div class="flex items-center justify-center"><div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mr-2"></div>Memuat data...</div>',
            search: 'Cari:',
            lengthMenu: 'Tampilkan _MENU_ data per halaman',
            info: 'Menampilkan _START_ sampai _END_ dari _TOTAL_ data',
            infoEmpty: 'Tidak ada data',
            infoFiltered: '(difilter dari _MAX_ total data)',
            paginate: {
                first: 'Pertama',
                last: 'Terakhir',
                next: 'Selanjutnya',
                previous: 'Sebelumnya'
            },
            emptyTable: 'Belum ada dokumen yang di-generate'
        },
        ajax: {
            url: '{{ route("documents.pegawai-documents") }}',
            type: 'GET',
            beforeSend: function() {
                showLoading();
            },
            complete: function(response) {
                hideLoading();
                updateStats(response.responseJSON);
            },
            error: function(xhr, error, thrown) {
                hideLoading();
                console.error('DataTables error:', error, thrown);

                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: 'Gagal memuat data pegawai. Silakan coba lagi.',
                    confirmButtonColor: '#EF4444'
                });
            }
        },
        columns: [
            { 
                data: 'nip',
                name: 'nip',
                className: 'font-mono text-sm'
            },
            { 
                data: 'nama',
                name: 'nama',
                className: 'font-medium'
            },
            { 
                data: 'golongan',
                name: 'golongan',
                className: 'text-center'
            },
            { 
                data: 'jabatan',
                name: 'jabatan',
                className: 'text-sm',
                render: function(data, type, row) {
                    if (data && data.length > 50) {
                        return '<span title="' + data + '">' + data.substring(0, 50) + '...</span>';
                    }
                    return data || '-';
                }
            },
            { 
                data: 'unit_kerja',
                name: 'unit_kerja',
                className: 'text-sm',
                render: function(data, type, row) {
                    if (data && data.length > 30) {
                        return '<span title="' + data + '">' + data.substring(0, 30) + '...</span>';
                    }
                    return data || '-';
                }
            },
            { 
                data: 'document_count',
                name: 'document_count',
                className: 'text-center',
                render: function(data, type, row) {
                    if (data > 0) {
                        return '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">' + data + ' dokumen</span>';
                    }
                    return '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">0 dokumen</span>';
                }
            },
            { 
                data: null,
                name: 'actions',
                orderable: false,
                searchable: false,
                className: 'text-center',
                render: function(data, type, row) {
                    if (row.document_count > 0) {
                        let actions = '<div class="flex items-center justify-center space-x-2">';

                        // View Documents Button
                        actions += '<button type="button" onclick="showDocuments(\'' + row.nip + '\', \'' + row.nama.replace(/'/g, "\\'") + '\')" ';
                        actions += 'class="inline-flex items-center px-3 py-1.5 bg-blue-600 hover:bg-blue-700 text-white text-xs font-medium rounded-lg transition-all duration-200 shadow-sm hover:shadow-md transform hover:scale-105">';
                        actions += '<svg class="w-3 h-3 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
                        actions += '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>';
                        actions += '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>';
                        actions += '</svg>👁️ Lihat</button>';

                        // Quick Download (latest document)
                        if (row.documents && row.documents[0]) {
                            const downloadUrl = '{{ route("documents.download-document", ":filename") }}'.replace(':filename', row.documents[0].filename);
                            actions += '<a href="' + downloadUrl + '" ';
                            actions += 'class="inline-flex items-center px-3 py-1.5 bg-green-600 hover:bg-green-700 text-white text-xs font-medium rounded-lg transition-all duration-200 shadow-sm hover:shadow-md transform hover:scale-105">';
                            actions += '<svg class="w-3 h-3 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
                            actions += '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>';
                            actions += '</svg>📥 Download</a>';
                        }

                        actions += '</div>';
                        return actions;
                    }
                    return '<div class="text-center"><span class="inline-flex items-center px-2 py-1 bg-gray-100 text-gray-500 text-xs rounded-full">📭 Belum ada dokumen</span></div>';
                }
            }
        ],
        pageLength: 25,
        lengthMenu: [[10, 25, 50, 100], [10, 25, 50, 100]],
        language: {
            processing: "Memproses...",
            search: "Cari:",
            lengthMenu: "Tampilkan _MENU_ data per halaman",
            info: "Menampilkan _START_ sampai _END_ dari _TOTAL_ data",
            infoEmpty: "Menampilkan 0 sampai 0 dari 0 data",
            infoFiltered: "(disaring dari _MAX_ total data)",
            loadingRecords: "Memuat...",
            zeroRecords: "Tidak ada data yang ditemukan",
            emptyTable: "Tidak ada data tersedia",
            paginate: {
                first: "Pertama",
                previous: "Sebelumnya",
                next: "Selanjutnya",
                last: "Terakhir"
            }
        },
        order: [[1, 'asc']] // Sort by nama
    });

    // Handle dropdown toggle
    window.toggleDropdown = function(button) {
        const dropdown = button.nextElementSibling;
        const isHidden = dropdown.classList.contains('hidden');
        
        // Close all other dropdowns
        document.querySelectorAll('.dropdown-menu').forEach(menu => {
            menu.classList.add('hidden');
        });
        
        // Toggle current dropdown
        if (isHidden) {
            dropdown.classList.remove('hidden');
        }
    };

    // Close dropdown when clicking outside
    document.addEventListener('click', function(event) {
        if (!event.target.closest('.relative')) {
            document.querySelectorAll('.dropdown-menu').forEach(menu => {
                menu.classList.add('hidden');
            });
        }
    });

    // Update stats function
    function updateStats(data) {
        if (data && data.data) {
            const totalPegawai = data.recordsTotal || 0;
            let totalDokumen = 0;
            let dokumenHariIni = 0;
            const today = new Date().toISOString().split('T')[0];

            data.data.forEach(function(pegawai) {
                totalDokumen += pegawai.document_count || 0;

                if (pegawai.documents) {
                    pegawai.documents.forEach(function(doc) {
                        if (doc.created_at && doc.created_at.startsWith(today)) {
                            dokumenHariIni++;
                        }
                    });
                }
            });

            // Animate counter updates
            animateCounter('#totalPegawai', totalPegawai);
            animateCounter('#totalDokumen', totalDokumen);
            animateCounter('#dokumenHariIni', dokumenHariIni);
        }
    }

    // Animate counter function
    function animateCounter(selector, targetValue) {
        const element = $(selector);
        const currentValue = parseInt(element.text()) || 0;
        const increment = Math.ceil((targetValue - currentValue) / 20);

        if (currentValue !== targetValue) {
            element.text(Math.min(currentValue + increment, targetValue));
            setTimeout(() => animateCounter(selector, targetValue), 50);
        }
    }

    // Refresh table function
    window.refreshTable = function() {
        table.ajax.reload(null, false);
    };

    // Show documents modal
    window.showDocuments = function(nip, nama) {
        $('#modalPegawaiInfo').text(nama + ' (' + nip + ')');
        $('#documentsContent').html('<div class="text-center py-8"><div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div><p class="mt-2 text-gray-600">Memuat dokumen...</p></div>');
        $('#documentsModal').removeClass('hidden').addClass('flex');

        // Find documents for this pegawai from table data
        const tableData = table.rows().data();
        let pegawaiData = null;

        for (let i = 0; i < tableData.length; i++) {
            if (tableData[i].nip === nip) {
                pegawaiData = tableData[i];
                break;
            }
        }

        if (pegawaiData && pegawaiData.documents) {
            let html = '';
            pegawaiData.documents.forEach(function(doc, index) {
                const downloadUrl = '{{ route("documents.download-document", ":filename") }}'.replace(':filename', doc.filename);
                const fileExists = doc.file_exists ? 'text-green-600' : 'text-red-600';
                const statusIcon = doc.file_exists ? '✅' : '❌';
                const statusText = doc.file_exists ? 'File tersedia' : 'File tidak ditemukan';

                html += `
                    <div class="bg-gray-50 rounded-lg p-4 hover:bg-gray-100 transition-colors">
                        <div class="flex items-center justify-between">
                            <div class="flex-1">
                                <div class="flex items-center space-x-2 mb-2">
                                    <span class="text-lg">📄</span>
                                    <h4 class="font-medium text-gray-900">${doc.template_name}</h4>
                                    <span class="${fileExists} text-xs">${statusIcon} ${statusText}</span>
                                </div>
                                <p class="text-sm text-gray-600 mb-1">
                                    <span class="font-mono">${doc.filename}</span>
                                </p>
                                <div class="flex items-center space-x-4 text-xs text-gray-500">
                                    <span>📅 ${doc.created_at}</span>
                                    <span>📊 ${formatFileSize(doc.size)}</span>
                                </div>
                            </div>
                            <div class="flex items-center space-x-2 ml-4">
                                ${doc.file_exists ?
                                    `<a href="${downloadUrl}"
                                       class="inline-flex items-center px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors shadow-sm">
                                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                        </svg>
                                        Download
                                    </a>` :
                                    `<span class="inline-flex items-center px-3 py-2 bg-gray-300 text-gray-500 text-sm font-medium rounded-lg cursor-not-allowed">
                                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728"></path>
                                        </svg>
                                        Tidak Tersedia
                                    </span>`
                                }
                            </div>
                        </div>
                    </div>
                `;
            });

            if (html === '') {
                html = '<div class="text-center py-8 text-gray-500">Belum ada dokumen yang di-generate</div>';
            }

            $('#documentsContent').html(html);
        } else {
            $('#documentsContent').html('<div class="text-center py-8 text-gray-500">Belum ada dokumen yang di-generate</div>');
        }
    };

    // Close documents modal
    window.closeDocumentsModal = function() {
        $('#documentsModal').addClass('hidden').removeClass('flex');
    };

    // Format file size
    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // Close modal when clicking outside
    $(document).on('click', function(e) {
        if ($(e.target).is('#documentsModal')) {
            closeDocumentsModal();
        }
    });
});

// Global refresh function
function refreshTable() {
    if (typeof table !== 'undefined') {
        table.ajax.reload(null, false);
    }
}
</script>
@endpush
