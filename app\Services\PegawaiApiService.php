<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Exception;

class PegawaiApiService
{
    private $baseUrl;
    private $authUrl;
    private $username;
    private $password;
    private $tokenTtl;
    private $checkInterval;

    public function __construct()
    {
        $config = config('services.api_pegawai');
        $this->baseUrl = $config['base_url'];
        $this->authUrl = $config['auth_url'];
        $this->username = $config['username'];
        $this->password = $config['password'];
        $this->tokenTtl = (int) $config['token_ttl'];
        $this->checkInterval = (int) $config['check_interval'];
    }

    /**
     * Login to API and get access token
     */
    public function login($username = null, $password = null)
    {
        // Use provided credentials or fall back to config
        $loginUsername = $username ?? $this->username;
        $loginPassword = $password ?? $this->password;

        try {
            $response = Http::timeout(30)->post($this->authUrl . '/login', [
                'username' => $loginUsername,
                'password' => $loginPassword,
            ]);

            if ($response->successful()) {
                $data = $response->json();

                if (isset($data['success']) && $data['success'] && isset($data['token'])) {
                    $token = $data['token'];
                    $expiresAt = now()->addSeconds($this->tokenTtl);

                    // Store token in cache with TTL
                    Cache::put('api_pegawai_token', $token, $expiresAt);
                    Cache::put('api_pegawai_token_expires_at', $expiresAt, $expiresAt);

                    Log::info('API Pegawai: Login successful', [
                        'user' => $data['user'] ?? null
                    ]);

                    return [
                        'success' => true,
                        'token' => $token,
                        'expires_at' => $expiresAt,
                        'user' => $data['user'] ?? null
                    ];
                }
            }

            Log::error('API Pegawai: Login failed', [
                'status' => $response->status(),
                'response' => $response->body()
            ]);

            return [
                'success' => false,
                'message' => 'Login failed: Invalid credentials or server error'
            ];

        } catch (Exception $e) {
            Log::error('API Pegawai: Login exception', [
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'Login failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Get current token from cache
     */
    public function getToken()
    {
        return Cache::get('api_pegawai_token');
    }

    /**
     * Check if token is valid and not expired
     */
    public function isTokenValid()
    {
        $token = $this->getToken();
        $expiresAt = Cache::get('api_pegawai_token_expires_at');
        
        if (!$token || !$expiresAt) {
            return false;
        }

        return now()->isBefore($expiresAt);
    }

    /**
     * Ensure we have a valid token, login if necessary
     */
    public function ensureValidToken()
    {
        if (!$this->isTokenValid()) {
            $loginResult = $this->login();
            return $loginResult['success'];
        }
        return true;
    }

    /**
     * Make direct request to API without authentication
     */
    private function makeDirectRequest($method, $endpoint, $data = [])
    {
        \Log::info('PegawaiApiService: Making direct request', [
            'method' => $method,
            'endpoint' => $endpoint,
            'full_url' => $this->baseUrl . $endpoint
        ]);

        try {
            $response = Http::timeout(30)
                ->withHeaders([
                    'Content-Type' => 'application/json',
                ])
                ->$method($this->baseUrl . $endpoint, $data);

            if ($response->successful()) {
                return [
                    'success' => true,
                    'data' => $response->json()
                ];
            }

            return [
                'success' => false,
                'message' => 'Request failed with status: ' . $response->status(),
                'data' => $response->json()
            ];
        } catch (\Exception $e) {
            \Log::error('PegawaiApiService: Direct request failed', [
                'error' => $e->getMessage(),
                'endpoint' => $endpoint
            ]);

            return [
                'success' => false,
                'message' => 'Request failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Make authenticated request to API
     */
    private function makeAuthenticatedRequest($method, $endpoint, $data = [])
    {
        if (!$this->ensureValidToken()) {
            return [
                'success' => false,
                'message' => 'Authentication failed'
            ];
        }

        $token = $this->getToken();

        \Log::info('PegawaiApiService: Making request', [
            'method' => $method,
            'endpoint' => $endpoint,
            'full_url' => $this->baseUrl . $endpoint,
            'has_token' => !empty($token),
            'token_preview' => $token ? substr($token, 0, 50) . '...' : 'NO_TOKEN'
        ]);

        try {
            $fullUrl = $this->baseUrl . $endpoint;
            $headers = [
                'Authorization' => 'Bearer ' . $token,
                'Content-Type' => 'application/json',
            ];

            \Log::info('PegawaiApiService: Full request details', [
                'method' => $method,
                'url' => $fullUrl,
                'headers' => $headers,
                'data' => $data
            ]);

            $response = Http::timeout(30)
                ->withHeaders($headers)
                ->$method($fullUrl, $data);

            if ($response->status() === 401) {
                // Token expired, try to login again
                Cache::forget('api_pegawai_token');
                Cache::forget('api_pegawai_token_expires_at');
                
                if ($this->ensureValidToken()) {
                    // Retry the request with new token
                    $newToken = $this->getToken();
                    $response = Http::timeout(30)
                        ->withHeaders([
                            'Authorization' => 'Bearer ' . $newToken,
                            'Content-Type' => 'application/json',
                        ])
                        ->$method($this->baseUrl . $endpoint, $data);
                }
            }

            if ($response->successful()) {
                return [
                    'success' => true,
                    'data' => $response->json()
                ];
            }

            Log::error('API Pegawai: Request failed', [
                'method' => $method,
                'endpoint' => $endpoint,
                'status' => $response->status(),
                'response' => $response->body()
            ]);

            return [
                'success' => false,
                'message' => 'API request failed: ' . $response->status()
            ];

        } catch (Exception $e) {
            Log::error('API Pegawai: Request exception', [
                'method' => $method,
                'endpoint' => $endpoint,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'Request failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Search pegawai by name or NIP
     */
    public function searchPegawai($query, $limit = 10)
    {
        $params = [
            'search' => $query,
            'limit' => $limit
        ];

        $endpoint = '/pegawai?' . http_build_query($params);

        \Log::info('PegawaiApiService: searchPegawai', [
            'query' => $query,
            'limit' => $limit,
            'endpoint' => $endpoint,
            'full_url' => $this->baseUrl . $endpoint
        ]);

        // Use authenticated request with token
        $result = $this->makeAuthenticatedRequest('get', $endpoint);

        \Log::info('PegawaiApiService: searchPegawai result', [
            'success' => $result['success'] ?? false,
            'data_count' => isset($result['data']['data']) ? count($result['data']['data']) : 0,
            'total' => $result['data']['total'] ?? 0
        ]);

        return $result;
    }

    /**
     * Get pegawai by NIP
     */
    public function getPegawaiByNip($nip)
    {
        $result = $this->makeAuthenticatedRequest('get', '/pegawai/' . $nip);
        return $result;
    }

    /**
     * Get all pegawai with pagination
     */
    public function getAllPegawai($page = 1, $limit = 20, $search = null)
    {
        $params = [
            'page' => $page,
            'limit' => $limit
        ];

        if ($search) {
            $params['search'] = $search;
        }

        $endpoint = '/pegawai?' . http_build_query($params);
        $result = $this->makeAuthenticatedRequest('get', $endpoint);
        return $result;
    }

    /**
     * Get pegawai with tempat kerja data
     */
    public function getPegawaiWithTempatKerja($query = null)
    {
        $params = [];
        if ($query) {
            $params['search'] = $query;
        }

        $endpoint = '/pegawai/tempat-kerja';
        if (!empty($params)) {
            $endpoint .= '?' . http_build_query($params);
        }

        $result = $this->makeAuthenticatedRequest('get', $endpoint);
        return $result;
    }

    /**
     * Logout from API
     */
    public function logout()
    {
        try {
            if ($this->isTokenValid()) {
                $token = $this->getToken();
                Http::timeout(30)
                    ->withHeaders([
                        'Authorization' => 'Bearer ' . $token,
                        'Content-Type' => 'application/json',
                    ])
                    ->post($this->authUrl . '/logout');
            }
        } catch (Exception $e) {
            Log::error('API Pegawai: Logout exception', [
                'error' => $e->getMessage()
            ]);
        } finally {
            // Clear token from cache regardless of logout success
            Cache::forget('api_pegawai_token');
            Cache::forget('api_pegawai_token_expires_at');
        }

        return ['success' => true];
    }

    /**
     * Get token expiration info
     */
    public function getTokenInfo()
    {
        $token = $this->getToken();
        $expiresAt = Cache::get('api_pegawai_token_expires_at');
        
        return [
            'has_token' => !empty($token),
            'is_valid' => $this->isTokenValid(),
            'expires_at' => $expiresAt,
            'seconds_until_expiry' => $expiresAt ? now()->diffInSeconds($expiresAt, false) : null
        ];
    }
}
