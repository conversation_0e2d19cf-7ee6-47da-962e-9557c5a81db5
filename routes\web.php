<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\PegawaiController;

Route::get('/', function () {
    return redirect()->route('pegawai.search');
});

// Pegawai routes
Route::prefix('pegawai')->name('pegawai.')->group(function () {
    Route::get('/search', [PegawaiController::class, 'search'])->name('search');
    Route::get('/{nip}', [PegawaiController::class, 'show'])->name('show');
});
