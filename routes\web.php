<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\PegawaiController;
use App\Http\Controllers\DocumentController;

Route::get('/', function () {
    return redirect()->route('pegawai.search');
});

// Pegawai routes
Route::prefix('pegawai')->name('pegawai.')->group(function () {
    Route::get('/search', [PegawaiController::class, 'search'])->name('search');
    Route::get('/{nip}', [PegawaiController::class, 'show'])->name('show');
});

// Document Generation Routes
Route::prefix('documents')->name('documents.')->group(function () {
    Route::get('/', [DocumentController::class, 'index'])->name('index');
    Route::post('/generate', [DocumentController::class, 'generate'])->name('generate');
    Route::post('/template-variables', [DocumentController::class, 'getTemplateVariables'])->name('template-variables');
    Route::get('/download/{filename}', [DocumentController::class, 'download'])->name('download');
});
