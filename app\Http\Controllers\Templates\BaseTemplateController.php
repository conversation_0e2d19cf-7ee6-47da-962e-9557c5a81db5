<?php

namespace App\Http\Controllers\Templates;

use App\Http\Controllers\Controller;
use App\Services\DocumentGeneratorService;
use App\Services\PegawaiApiService;
use App\Models\GeneratedDocument;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

abstract class BaseTemplateController extends Controller
{
    protected $documentGenerator;
    protected $pegawaiApiService;
    protected $templateName;
    protected $templateDisplayName;
    protected $requiredFields = [];
    protected $optionalFields = [];

    public function __construct(DocumentGeneratorService $documentGenerator, PegawaiApiService $pegawaiApiService)
    {
        $this->documentGenerator = $documentGenerator;
        $this->pegawaiApiService = $pegawaiApiService;
    }

    /**
     * Show the template form
     */
    public function index()
    {
        return view('templates.' . $this->getTemplateViewName(), [
            'templateName' => $this->templateDisplayName,
            'requiredFields' => $this->requiredFields,
            'optionalFields' => $this->optionalFields
        ]);
    }

    /**
     * Generate document from template
     */
    public function generate(Request $request)
    {
        try {
            // Validate required fields
            $this->validateRequest($request);

            // Get pegawai data
            $pegawaiResult = $this->pegawaiApiService->getPegawaiByNip($request->nip_pegawai);
            if (!$pegawaiResult['success']) {
                return response()->json([
                    'success' => false,
                    'message' => 'Data pegawai tidak ditemukan: ' . ($pegawaiResult['message'] ?? 'Unknown error')
                ]);
            }

            // Get pejabat data
            $pejabatResult = $this->pegawaiApiService->getPegawaiByNip($request->nip_pejabat);
            if (!$pejabatResult['success']) {
                return response()->json([
                    'success' => false,
                    'message' => 'Data pejabat tidak ditemukan: ' . ($pejabatResult['message'] ?? 'Unknown error')
                ]);
            }

            $pegawaiData = $pegawaiResult['data'];
            $pejabatData = $pejabatResult['data'];

            // Prepare template data (template-specific)
            $templateData = $this->prepareTemplateData($pegawaiData, $pejabatData, $request->all());

            // Generate custom filename
            $customFileName = $this->generateFileName($pegawaiData, $request);

            // Generate document
            $result = $this->documentGenerator->generateDocument(
                $this->templateName,
                $templateData,
                $customFileName
            );

            if ($result['success']) {
                // Save to database
                $this->saveDocumentRecord($pegawaiData, $pejabatData, $result, $templateData, $request);

                Log::info('Template: Document generated successfully', [
                    'template' => $this->templateName,
                    'pegawai_nip' => $pegawaiData['nip'],
                    'filename' => $result['file_name']
                ]);
            }

            return response()->json($result);

        } catch (\Exception $e) {
            Log::error('Template: Generation failed', [
                'template' => $this->templateName,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Validate request (can be overridden by child classes)
     */
    protected function validateRequest(Request $request)
    {
        $rules = [
            'nip_pegawai' => 'required|string|size:18',
            'nip_pejabat' => 'required|string|size:18'
        ];

        // Add template-specific validation rules
        foreach ($this->requiredFields as $field) {
            $rules[$field] = 'required|string';
        }

        $request->validate($rules);
    }

    /**
     * Generate filename (can be overridden by child classes)
     */
    protected function generateFileName($pegawaiData, $request)
    {
        $pegawaiNip = $pegawaiData['nip'] ?? '';
        $templateBaseName = pathinfo($this->templateName, PATHINFO_FILENAME);
        return $pegawaiNip . '_' . $templateBaseName . '_' . date('Y-m-d_H-i-s') . '.docx';
    }

    /**
     * Save document record to database
     */
    protected function saveDocumentRecord($pegawaiData, $pejabatData, $result, $templateData, $request)
    {
        try {
            $templateName = $this->extractTemplateNameFromFilename($this->templateName);
            $fileSize = file_exists($result['file_path']) ? filesize($result['file_path']) : 0;
            $generatedBy = $request->ip();

            GeneratedDocument::create([
                'pegawai_nip' => $pegawaiData['nip'] ?? '',
                'pegawai_nama' => $pegawaiData['nama'] ?? '',
                'pegawai_golongan' => $pegawaiData['golongan'] ?? '',
                'pegawai_jabatan' => $pegawaiData['jabatan'] ?? '',
                'pegawai_unit_kerja' => $pegawaiData['unit_kerja'] ?? '',
                
                'pejabatttd' => $pejabatData['nama'] ?? '',
                'nippejabatttd' => $pejabatData['nip'] ?? '',
                
                'template_name' => $templateName,
                'template_filename' => $this->templateName,
                'generated_filename' => $result['file_name'],
                'file_path' => $result['file_path'],
                'file_size' => $fileSize,
                
                'generated_by' => $generatedBy,
                'generated_at' => now(),
                'template_variables' => $templateData
            ]);

        } catch (\Exception $e) {
            Log::error('Template: Failed to save document record', [
                'error' => $e->getMessage(),
                'template' => $this->templateName
            ]);
        }
    }

    /**
     * Extract template name from filename
     */
    protected function extractTemplateNameFromFilename($filename)
    {
        return pathinfo($filename, PATHINFO_FILENAME);
    }

    /**
     * Get template view name (can be overridden)
     */
    protected function getTemplateViewName()
    {
        return strtolower(str_replace(['Controller', 'Template'], '', class_basename($this)));
    }

    // Abstract methods that must be implemented by child classes
    abstract protected function prepareTemplateData($pegawaiData, $pejabatData, $requestData);
}
